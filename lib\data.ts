import { Product, Category } from '@/types';

export const products: Product[] = [
  {
    id: 1,
    name: '时尚连衣裙',
    price: 299,
    originalPrice: 399,
    image: 'https://picsum.photos/seed/dress1/400/400',
    category: 'women',
    description: '优雅的夏季连衣裙，采用高品质面料制作，舒适透气。',
    inStock: true,
    featured: true,
  },
  {
    id: 2,
    name: '商务西装',
    price: 899,
    image: 'https://picsum.photos/seed/suit1/400/400',
    category: 'men',
    description: '经典商务西装，适合正式场合穿着。',
    inStock: true,
    featured: true,
  },
  {
    id: 3,
    name: '休闲T恤',
    price: 89,
    image: 'https://picsum.photos/seed/tshirt1/400/400',
    category: 'men',
    description: '舒适的纯棉T恤，日常休闲必备。',
    inStock: true,
  },
  {
    id: 4,
    name: '牛仔裤',
    price: 199,
    image: 'https://picsum.photos/seed/jeans1/400/400',
    category: 'women',
    description: '经典牛仔裤，百搭时尚。',
    inStock: true,
  },
  {
    id: 5,
    name: '运动鞋',
    price: 399,
    originalPrice: 499,
    image: 'https://picsum.photos/seed/shoes1/400/400',
    category: 'shoes',
    description: '舒适的运动鞋，适合日常运动。',
    inStock: true,
    featured: true,
  },
  {
    id: 6,
    name: '手提包',
    price: 259,
    image: 'https://picsum.photos/seed/bag1/400/400',
    category: 'accessories',
    description: '时尚手提包，实用美观。',
    inStock: true,
  },
];

export const categories: Category[] = [
  {
    id: 'women',
    name: '女装',
    image: 'https://picsum.photos/seed/women/300/200',
    productCount: 2,
  },
  {
    id: 'men',
    name: '男装',
    image: 'https://picsum.photos/seed/men/300/200',
    productCount: 2,
  },
  {
    id: 'shoes',
    name: '鞋类',
    image: 'https://picsum.photos/seed/shoes/300/200',
    productCount: 1,
  },
  {
    id: 'accessories',
    name: '配饰',
    image: 'https://picsum.photos/seed/accessories/300/200',
    productCount: 1,
  },
];

export function getProducts(): Product[] {
  return products;
}

export function getProduct(id: number): Product | undefined {
  return products.find(p => p.id === id);
}

export function getProductsByCategory(category: string): Product[] {
  return products.filter(p => p.category === category);
}

export function getFeaturedProducts(): Product[] {
  return products.filter(p => p.featured);
}

export function getCategories(): Category[] {
  return categories;
}
