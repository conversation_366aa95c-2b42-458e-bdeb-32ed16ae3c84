'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { IMAGES } from '@/utils/imageConstants';

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState('profile');

  // 模拟用户数据
  const user = {
    name: '张三',
    email: 'z<PERSON><PERSON>@example.com',
    phone: '138****8888',
    avatar: IMAGES.avatar('user-1'),
  };

  // 模拟订单数据
  const orders = [
    {
      id: '***********',
      date: '2023-12-01',
      status: '已完成',
      total: 299,
      items: 2,
    },
    {
      id: '***********',
      date: '2023-11-25',
      status: '配送中',
      total: 189,
      items: 1,
    },
  ];

  const tabs = [
    { id: 'profile', name: '个人信息', icon: '👤' },
    { id: 'orders', name: '我的订单', icon: '📦' },
    { id: 'addresses', name: '收货地址', icon: '📍' },
    { id: 'favorites', name: '我的收藏', icon: '❤️' },
    { id: 'settings', name: '账户设置', icon: '⚙️' },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            {/* 用户信息 */}
            <div className="text-center mb-6">
              <div className="w-20 h-20 mx-auto mb-4 rounded-full overflow-hidden">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <h2 className="text-xl font-semibold">{user.name}</h2>
              <p className="text-gray-600">{user.email}</p>
            </div>

            {/* 导航菜单 */}
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-md transition-colors duration-200 flex items-center space-x-3 ${
                    activeTab === tab.id
                      ? 'bg-indigo-50 text-indigo-600 border-l-4 border-indigo-600'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 主内容区 */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md p-6">
            {activeTab === 'profile' && (
              <div>
                <h3 className="text-2xl font-semibold mb-6">个人信息</h3>
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="form-control">
                      <label htmlFor="name">姓名</label>
                      <input
                        type="text"
                        id="name"
                        defaultValue={user.name}
                        className="form-input"
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="email">邮箱</label>
                      <input
                        type="email"
                        id="email"
                        defaultValue={user.email}
                        className="form-input"
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="phone">手机号</label>
                      <input
                        type="tel"
                        id="phone"
                        defaultValue={user.phone}
                        className="form-input"
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="birthday">生日</label>
                      <input
                        type="date"
                        id="birthday"
                        className="form-input"
                      />
                    </div>
                  </div>
                  <button type="submit" className="btn btn-primary">
                    保存更改
                  </button>
                </form>
              </div>
            )}

            {activeTab === 'orders' && (
              <div>
                <h3 className="text-2xl font-semibold mb-6">我的订单</h3>
                <div className="space-y-4">
                  {orders.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-semibold">订单号: {order.id}</h4>
                          <p className="text-gray-600">下单时间: {order.date}</p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-sm ${
                          order.status === '已完成' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">{order.items} 件商品</span>
                        <div className="flex items-center space-x-4">
                          <span className="font-semibold">¥{order.total}</span>
                          <Link href={`/orders/${order.id}`} className="btn btn-outline btn-sm">
                            查看详情
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'addresses' && (
              <div>
                <h3 className="text-2xl font-semibold mb-6">收货地址</h3>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold">张三</h4>
                        <p className="text-gray-600">138****8888</p>
                        <p className="text-gray-600">北京市朝阳区xxx街道xxx号</p>
                      </div>
                      <div className="flex space-x-2">
                        <button className="btn btn-outline btn-sm">编辑</button>
                        <button className="btn btn-outline btn-sm text-red-600">删除</button>
                      </div>
                    </div>
                  </div>
                  <button className="btn btn-primary">添加新地址</button>
                </div>
              </div>
            )}

            {activeTab === 'favorites' && (
              <div>
                <h3 className="text-2xl font-semibold mb-6">我的收藏</h3>
                <p className="text-gray-600">您还没有收藏任何商品</p>
                <Link href="/products" className="btn btn-primary mt-4">
                  去逛逛
                </Link>
              </div>
            )}

            {activeTab === 'settings' && (
              <div>
                <h3 className="text-2xl font-semibold mb-6">账户设置</h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-medium mb-4">密码设置</h4>
                    <form className="space-y-4">
                      <div className="form-control">
                        <label htmlFor="current-password">当前密码</label>
                        <input
                          type="password"
                          id="current-password"
                          className="form-input"
                        />
                      </div>
                      <div className="form-control">
                        <label htmlFor="new-password">新密码</label>
                        <input
                          type="password"
                          id="new-password"
                          className="form-input"
                        />
                      </div>
                      <div className="form-control">
                        <label htmlFor="confirm-password">确认新密码</label>
                        <input
                          type="password"
                          id="confirm-password"
                          className="form-input"
                        />
                      </div>
                      <button type="submit" className="btn btn-primary">
                        更新密码
                      </button>
                    </form>
                  </div>
                  
                  <div className="border-t pt-6">
                    <h4 className="text-lg font-medium mb-4 text-red-600">危险操作</h4>
                    <button className="btn bg-red-600 text-white hover:bg-red-700">
                      删除账户
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
