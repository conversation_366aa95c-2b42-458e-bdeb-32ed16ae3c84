@import "tailwindcss";

:root {
  /* 主要颜色 - 与Tailwind配置保持一致 */
  --primary: #3f51b5;
  --primary-dark: #303f9f;
  --primary-light: #7986cb;
  --secondary: #f50057;
  --secondary-dark: #c51162;
  --secondary-light: #ff5983;

  /* 背景和前景色 */
  --background: #ffffff;
  --foreground: #171717;

  /* 灰度色阶 - 与Tailwind gray配置一致 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;

  /* 状态颜色 */
  --success: #4caf50;
  --error: #f44336;
  --warning: #ff9800;
  --info: #2196f3;

  /* RGB格式颜色（用于透明度和渐变） */
  --primary-rgb: 63, 81, 181;
  --primary-dark-rgb: 48, 63, 159;
  --secondary-rgb: 245, 0, 87;
  --secondary-dark-rgb: 197, 17, 98;
  --foreground-rgb: 23, 23, 23;
  --background-rgb: 255, 255, 255;

  /* 渐变背景 */
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 255, 255, 255;

  /* 字体变量 */
  --font-geist-sans: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-geist-mono: 'Geist Mono', Menlo, Monaco, Consolas, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --background-rgb: 10, 10, 10;
    --foreground-rgb: 237, 237, 237;

    /* 深色模式下的灰度色阶 */
    --gray-50: #212121;
    --gray-100: #212121;
    --gray-200: #424242;
    --gray-300: #616161;
    --gray-400: #757575;
    --gray-500: #9e9e9e;
    --gray-600: #bdbdbd;
    --gray-700: #e0e0e0;
    --gray-800: #eeeeee;
    --gray-900: #f5f5f5;

    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* 全局样式 */
body {
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  color: rgb(var(--foreground-rgb));
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* 移除重复的传统CSS类定义，统一使用Tailwind的@layer components */

@layer components {
  /* 容器样式 */
  .container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md font-medium py-2 px-4 transition-all duration-200 cursor-pointer border border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
  }

  .btn-secondary {
    @apply bg-pink-600 text-white hover:bg-pink-700 focus:ring-pink-500;
  }

  .btn-outline {
    @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500;
  }

  .btn-sm {
    @apply py-1.5 px-3 text-sm;
  }

  .btn-lg {
    @apply py-3 px-6 text-lg;
  }

  /* 表单控件样式 - 统一所有表单的外观 */
  .form-control {
    @apply mb-6;
  }

  .form-control label {
    @apply block mb-2 font-semibold text-gray-700 text-sm;
  }

  .form-control input,
  .form-control textarea,
  .form-control select {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 shadow-sm;
  }

  .form-control input:hover,
  .form-control textarea:hover,
  .form-control select:hover {
    @apply border-gray-400;
  }

  .form-control input:focus,
  .form-control textarea:focus,
  .form-control select:focus {
    @apply shadow-md;
  }

  .form-control input:disabled,
  .form-control textarea:disabled,
  .form-control select:disabled {
    @apply bg-gray-100 text-gray-500 cursor-not-allowed border-gray-200;
  }

  .form-control input.error,
  .form-control textarea.error,
  .form-control select.error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }

  .form-control .error-message {
    @apply text-red-600 text-sm mt-1 font-medium;
  }

  .form-control .help-text {
    @apply text-gray-500 text-sm mt-1;
  }

  /* 复选框和单选框样式 */
  .form-control input[type="checkbox"],
  .form-control input[type="radio"] {
    @apply w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-2;
  }

  .form-control input[type="checkbox"] {
    @apply rounded;
  }

  .form-control input[type="radio"] {
    @apply rounded-full;
  }

  /* 表单组样式 */
  .form-group {
    @apply space-y-6;
  }

  .form-row {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  /* 表单卡片样式 */
  .form-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 p-8 max-w-md mx-auto;
  }

  .form-card-header {
    @apply text-center mb-8;
  }

  .form-card-title {
    @apply text-3xl font-bold text-gray-900 mb-2;
  }

  .form-card-subtitle {
    @apply text-gray-600;
  }

  .form-card-footer {
    @apply mt-8 text-center text-sm text-gray-600;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 transition-shadow duration-200 hover:shadow-lg;
  }

  /* 分类横幅样式 */
  .category-banner {
    @apply relative w-full h-64 mb-8 rounded-lg overflow-hidden;
  }

  .category-title {
    @apply absolute inset-0 flex items-center justify-center bg-black/40 text-white;
  }

  .category-title h1 {
    @apply text-4xl font-bold text-center;
  }

  /* 产品卡片样式 - 统一所有产品卡片的外观 */
  .product-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group;
  }

  .product-image {
    @apply relative w-full overflow-hidden;
    aspect-ratio: 3/4; /* 统一宽高比 */
  }

  .product-image-img {
    @apply object-cover w-full h-full transition-transform duration-500 group-hover:scale-110;
  }

  .product-badges {
    @apply absolute top-3 left-3 z-10 flex flex-col gap-2;
  }

  .product-actions {
    @apply absolute top-3 right-3 z-10 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300;
  }

  .product-action-btn {
    @apply bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-110;
  }

  .product-info {
    @apply p-4 space-y-2;
  }

  .product-category {
    @apply text-sm text-gray-500 font-medium uppercase tracking-wide;
  }

  .product-name {
    @apply text-lg font-semibold text-gray-900 line-clamp-2 hover:text-primary transition-colors duration-200 cursor-pointer;
  }

  .product-price-container {
    @apply flex items-center justify-between;
  }

  .product-price {
    @apply text-xl font-bold text-primary;
  }

  .product-original-price {
    @apply text-sm line-through text-gray-400 ml-2;
  }

  .product-discount {
    @apply text-sm font-medium text-green-600;
  }

  .product-add-to-cart {
    @apply w-full mt-3 btn btn-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300;
  }

  /* 徽章样式 */
  .badge {
    @apply px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-indigo-600 text-white;
  }

  .badge-secondary {
    @apply bg-pink-600 text-white;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }

  /* 网格布局 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-8;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* 文本样式 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }

  /* 动画效果 */
  .fade-in {
    @apply opacity-0 animate-pulse;
  }

  .fade-in.loaded {
    @apply opacity-100 animate-none transition-opacity duration-500;
  }
}

/* Tailwind CSS v4 兼容性类 */
@layer utilities {
  .bg-primary {
    background-color: #3f51b5;
  }

  .bg-primary-500 {
    background-color: #3f51b5;
  }

  .text-primary {
    color: #3f51b5;
  }

  .text-primary-500 {
    color: #3f51b5;
  }

  .border-primary {
    border-color: #3f51b5;
  }

  .focus\:ring-primary-500:focus {
    --tw-ring-color: #3f51b5;
  }

  .hover\:bg-primary:hover {
    background-color: #303f9f;
  }

  .hover\:text-primary:hover {
    color: #3f51b5;
  }

  .bg-opacity-40 {
    background-color: rgb(0 0 0 / 0.4);
  }

  .bg-black\/40 {
    background-color: rgb(0 0 0 / 0.4);
  }

  .bg-black\/50 {
    background-color: rgb(0 0 0 / 0.5);
  }

  /* Group 功能 */
  .group {
    position: relative;
  }

  .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
  }

  .group:hover .group-hover\:bg-black\/50 {
    background-color: rgb(0 0 0 / 0.5);
  }

  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }

  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }
}
