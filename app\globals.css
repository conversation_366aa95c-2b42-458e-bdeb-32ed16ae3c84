@import "tailwindcss";

:root {
  --primary: #3f51b5;
  --primary-dark: #303f9f;
  --secondary: #f50057;
  --background: #ffffff;
  --foreground: #171717;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  --success: #4caf50;
  --error: #f44336;
  --warning: #ff9800;
  --info: #2196f3;
  --primary-color: 63 81 181; /* #3f51b5 */
  --primary-dark-color: 48 63 159; /* #303f9f */
  --secondary-color: 245 0 87; /* #f50057 */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 255, 255, 255;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --gray-100: #212121;
    --gray-200: #424242;
    --gray-300: #616161;
    --gray-400: #757575;
    --gray-500: #9e9e9e;
    --gray-600: #bdbdbd;
    --gray-700: #e0e0e0;
    --gray-800: #eeeeee;
    --gray-900: #f5f5f5;
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  color: rgb(var(--foreground-rgb));
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: #c51162;
}

.card {
  background-color: var(--background);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  overflow: hidden;
}

.form-control {
  margin-bottom: 1rem;
}

.form-control label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control input,
.form-control textarea,
.form-control select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--gray-300);
  border-radius: 0.25rem;
  background-color: var(--background);
  color: var(--foreground);
}

.form-control input:focus,
.form-control textarea:focus,
.form-control select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
}

.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 480px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@layer components {
  .container {
    @apply w-full max-w-7xl mx-auto px-4;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded font-medium py-2 px-4 transition duration-200 cursor-pointer;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-gray-800;
  }

  .btn-secondary {
    @apply bg-pink-600 text-white hover:bg-pink-700;
  }

  .form-control {
    @apply mb-4;
  }

  .form-control label {
    @apply block mb-2 font-medium;
  }

  .form-control input,
  .form-control textarea,
  .form-control select {
    @apply w-full p-2 border border-gray-300 rounded bg-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  .category-banner {
    @apply relative w-full h-64 mb-8;
  }

  .category-title {
    @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 text-white;
  }

  .category-title h1 {
    @apply text-4xl font-bold;
  }

  .product-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .product-image {
    @apply relative w-full h-64 overflow-hidden;
  }

  .product-badge {
    @apply absolute top-2 right-2 z-10;
  }

  .badge {
    @apply px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .product-info {
    @apply p-4;
  }

  .product-name {
    @apply text-lg font-medium line-clamp-2;
  }

  .product-price {
    @apply text-lg font-bold text-primary;
  }

  .product-original-price {
    @apply text-sm line-through text-gray-400 ml-2;
  }
}
