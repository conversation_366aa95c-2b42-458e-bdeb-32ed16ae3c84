import React from 'react';
import { IMAGES } from '@/utils/imageConstants';
import OptimizedImage from '@/components/ui/OptimizedImage';

export default function AboutPage() {
  return (
    <div>
      {/* 页面横幅 */}
      <div className="relative h-[400px] mb-16">
        <OptimizedImage
          src={IMAGES.hero.fashion}
          alt="关于我们"
          fill
          className="object-cover"
          priority
          fallbackType="hero"
          fallbackId="about"
        />
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4">关于我们</h1>
            <p className="text-xl">时尚源于生活，品质成就未来</p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4">
        {/* 品牌故事 */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">我们的故事</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  时尚电商平台成立于2010年，始终秉承"时尚源于生活"的品牌理念。我们相信，真正的时尚不仅仅是外表的装饰，更是内心品味和生活态度的体现。
                </p>
                <p>
                  十多年来，我们专注于为顾客提供高品质、富有设计感的时尚产品。从最初的小型工作室，到如今拥有完整供应链的时尚品牌，我们始终坚持对品质的追求和对设计的热爱。
                </p>
                <p>
                  我们的设计团队遍布全球各大时尚之都，紧跟国际潮流趋势，同时融入东方美学元素，为每一位顾客打造独特而优雅的时尚体验。
                </p>
              </div>
            </div>
            <div className="relative h-[400px] rounded-lg overflow-hidden">
              <OptimizedImage
                src={IMAGES.hero.main}
                alt="品牌故事"
                fill
                className="object-cover"
                fallbackType="hero"
                fallbackId="story"
              />
            </div>
          </div>
        </section>

        {/* 核心价值 */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">我们的价值观</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-indigo-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">品质至上</h3>
              <p className="text-gray-600">
                我们严格把控每一个生产环节，确保每件产品都达到最高的品质标准，为顾客提供值得信赖的时尚选择。
              </p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-pink-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">用心设计</h3>
              <p className="text-gray-600">
                每一个设计都倾注了我们的心血，我们相信细节决定成败，用心设计每一件作品，让时尚更有温度。
              </p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">客户为本</h3>
              <p className="text-gray-600">
                顾客的满意是我们最大的追求，我们倾听每一位顾客的声音，不断改进服务，提供更好的购物体验。
              </p>
            </div>
          </div>
        </section>

        {/* 团队介绍 */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">我们的团队</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                <OptimizedImage
                  src={IMAGES.avatar('ceo')}
                  alt="CEO"
                  width={128}
                  height={128}
                  className="object-cover"
                  fallbackType="avatar"
                  fallbackId="ceo"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">李明</h3>
              <p className="text-indigo-600 mb-3">创始人 & CEO</p>
              <p className="text-gray-600 text-sm">
                拥有15年时尚行业经验，曾在多家国际知名时尚品牌担任高级管理职位。
              </p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                <OptimizedImage
                  src={IMAGES.avatar('designer')}
                  alt="设计总监"
                  width={128}
                  height={128}
                  className="object-cover"
                  fallbackType="avatar"
                  fallbackId="designer"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">王雅</h3>
              <p className="text-indigo-600 mb-3">设计总监</p>
              <p className="text-gray-600 text-sm">
                毕业于巴黎时装学院，拥有丰富的国际时尚设计经验，擅长将东西方美学完美融合。
              </p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                <OptimizedImage
                  src={IMAGES.avatar('manager')}
                  alt="运营总监"
                  width={128}
                  height={128}
                  className="object-cover"
                  fallbackType="avatar"
                  fallbackId="manager"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">张伟</h3>
              <p className="text-indigo-600 mb-3">运营总监</p>
              <p className="text-gray-600 text-sm">
                专注于电商运营和客户体验优化，致力于为每位顾客提供最优质的购物服务。
              </p>
            </div>
          </div>
        </section>

        {/* 联系信息 */}
        <section className="mb-16 bg-gray-50 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-center mb-8">联系我们</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">公司信息</h3>
              <div className="space-y-3 text-gray-600">
                <p><strong>地址：</strong>北京市朝阳区时尚大厦18层</p>
                <p><strong>电话：</strong>400-888-8888</p>
                <p><strong>邮箱：</strong><EMAIL></p>
                <p><strong>营业时间：</strong>周一至周日 9:00-21:00</p>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">关注我们</h3>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-indigo-600 text-white rounded-full flex items-center justify-center hover:bg-indigo-700 transition-colors">
                  微
                </a>
                <a href="#" className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                  微
                </a>
                <a href="#" className="w-10 h-10 bg-pink-600 text-white rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors">
                  小
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
