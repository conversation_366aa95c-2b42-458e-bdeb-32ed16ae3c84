import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { FALLBACK_PRODUCTS } from '@/utils/mockData';
import ProductImageGallery from '@/components/product/ProductImageGallery';

// 注意：revalidate 不能在客户端组件中使用

interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Attribute {
  id: number;
  name: string;
  position: number;
  visible: boolean;
  variation: boolean;
  options: string[];
}

interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  images: ProductImage[];
  categories: Category[];
  attributes?: Attribute[];
  stock_quantity?: number;
  stock_status?: string;
}

export default async function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // 等待params解析
  const { id } = await params;

  // 直接从静态数据中获取产品
  const product = FALLBACK_PRODUCTS.find(p => p.id.toString() === id) as Product;
  
  // 如果找不到产品，返回404
  if (!product) {
    notFound();
  }


  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <nav className="text-sm text-gray-500">
          <ol className="flex flex-wrap items-center">
            <li>
              <Link href="/" className="hover:text-primary">首页</Link>
              <span className="mx-2">/</span>
            </li>
            {product.categories && product.categories.length > 0 && (
              <li>
                <Link href={`/catalog/${product.categories[0].slug}`} className="hover:text-primary">
                  {product.categories[0].name}
                </Link>
                <span className="mx-2">/</span>
              </li>
            )}
            <li className="text-gray-800 font-medium">{product.name}</li>
          </ol>
        </nav>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-12">
        {/* 产品图片 */}
        <ProductImageGallery
          images={product.images || []}
          productName={product.name}
          productId={Number(product.id)}
          onSale={product.on_sale}
          stockStatus={product.stock_status}
        />
        
        {/* 产品信息 */}
        <div>
          <h1 className="text-3xl font-bold mb-4">{product.name}</h1>
          
          {/* 价格 */}
          <div className="mb-6">
            {product.on_sale ? (
              <div className="flex items-center">
                <span className="text-3xl font-bold text-primary">¥{product.sale_price}</span>
                <span className="ml-3 text-xl text-gray-500 line-through">¥{product.regular_price}</span>
                <span className="ml-3 px-2 py-1 bg-red-100 text-red-600 text-sm font-medium rounded">
                  省 ¥{(Number(product.regular_price) - Number(product.sale_price)).toFixed(2)}
                </span>
              </div>
            ) : (
              <span className="text-3xl font-bold text-primary">¥{product.price}</span>
            )}
          </div>

          {/* 简短描述 */}
          {product.short_description && (
            <div className="mb-6 text-gray-600" dangerouslySetInnerHTML={{ __html: product.short_description }} />
          )}
          
          {/* 库存状态 */}
          <div className="mb-6">
            <div className="flex items-center">
              <span className="mr-2 text-gray-700">库存状态:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                product.stock_status === 'instock' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {product.stock_status === 'instock' ? '有库存' : '缺货'}
              </span>
            </div>
            {typeof product.stock_quantity === 'number' && (
              <div className="mt-1 text-sm text-gray-500">
                剩余 {product.stock_quantity} 件
              </div>
            )}
          </div>
          
          {/* 属性选择 */}
          {product.attributes && product.attributes.length > 0 && (
            <div className="space-y-4 mb-6">
              {product.attributes.map((attr, index) => (
                <div key={index}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {attr.name}
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {attr.options.map((option, optIndex) => (
                      <button
                        key={optIndex}
                        className="px-4 py-2 border border-gray-300 rounded-md text-sm hover:border-primary hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
                      >
                        {option}
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* 数量和添加到购物车 */}
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <div className="w-32">
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
                数量
              </label>
              <div className="flex border border-gray-300 rounded-md">
                <button className="px-3 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-l-md">-</button>
                <input
                  type="text"
                  id="quantity"
                  className="flex-1 text-center border-0 focus:ring-0"
                  defaultValue="1"
                  readOnly
                />
                <button className="px-3 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-r-md">+</button>
              </div>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2 sm:opacity-0">
                操作
              </label>
              <button 
                className={`w-full btn ${
                  product.stock_status === 'outofstock' 
                    ? 'bg-gray-300 text-gray-600 cursor-not-allowed' 
                    : 'btn-primary bg-primary text-white'
                }`}
                disabled={product.stock_status === 'outofstock'}
              >
                {product.stock_status === 'outofstock' ? '缺货' : '加入购物车'}
              </button>
            </div>
          </div>
          
          {/* 分类 */}
          {product.categories && product.categories.length > 0 && (
            <div className="mb-4">
              <span className="text-gray-700">分类:</span>
              <div className="flex flex-wrap gap-2 mt-1">
                {product.categories.map((category, index) => (
                  <Link
                    key={index}
                    href={`/catalog/${category.slug}`}
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200"
                  >
                    {category.name}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 产品详情 */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">产品详情</h2>
        <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: product.description }} />
      </div>
    </div>
  );
} 