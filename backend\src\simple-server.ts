import express from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// 创建Express应用
const app = express();

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// 测试路由
app.get('/', (req, res) => {
  res.json({
    message: '时尚服饰电商API正在运行',
    status: 'success'
  });
});

// 模拟用户API
app.post('/api/auth/register', (req, res) => {
  const { username, email, password } = req.body;
  
  // 简单验证
  if (!username || !email || !password) {
    return res.status(400).json({
      success: false,
      message: '请提供所有必填字段'
    });
  }
  
  // 模拟用户注册成功
  res.status(201).json({
    success: true,
    token: 'mock-token-12345',
    user: {
      id: 'user-id-123',
      username,
      email,
      role: 'user'
    }
  });
});

app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // 简单验证
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: '请提供邮箱和密码'
    });
  }
  
  // 模拟用户登录成功
  res.status(200).json({
    success: true,
    token: 'mock-token-12345',
    user: {
      id: 'user-id-123',
      username: 'demo_user',
      email,
      role: 'user'
    }
  });
});

// 模拟产品API
app.get('/api/products', (req, res) => {
  // 返回模拟产品数据
  res.json({
    success: true,
    products: [
      {
        id: '1',
        name: '夏季轻薄连衣裙',
        price: 299,
        originalPrice: 399,
        image: '/images/product1.jpg',
        category: '女装',
        isNew: true,
        isSale: true,
      },
      {
        id: '2',
        name: '男士休闲西装外套',
        price: 599,
        originalPrice: 699,
        image: '/images/product2.jpg',
        category: '男装',
        isNew: false,
        isSale: true,
      },
      {
        id: '3',
        name: '时尚牛仔裤',
        price: 199,
        image: '/images/product3.jpg',
        category: '女装',
        isNew: true,
        isSale: false,
      },
      {
        id: '4',
        name: '运动休闲鞋',
        price: 399,
        image: '/images/product4.jpg',
        category: '鞋子',
        isNew: false,
        isSale: false,
      },
    ],
  });
});

// 启动服务器
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`简化版服务器运行在端口: ${PORT}`);
});