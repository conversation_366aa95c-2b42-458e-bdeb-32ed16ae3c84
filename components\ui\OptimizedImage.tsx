'use client';

import Image from 'next/image';
import { useState } from 'react';

interface OptimizedImageProps {
  src?: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  quality?: number;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  fallbackType?: 'product' | 'category' | 'hero' | 'avatar';
  fallbackId?: number;
}

// 生成占位图片URL的函数
const generatePlaceholderUrl = (
  type: string, 
  width: number = 400, 
  height: number = 400, 
  id?: number
): string => {
  const baseUrl = 'https://picsum.photos';
  
  switch (type) {
    case 'product':
      // 使用特定的种子来确保同一产品总是显示相同的图片
      const seed = id ? `product-${id}` : 'product-default';
      return `${baseUrl}/seed/${seed}/${width}/${height}`;
    
    case 'category':
      const categorySeed = id ? `category-${id}` : 'category-default';
      return `${baseUrl}/seed/${categorySeed}/${width}/${height}`;
    
    case 'hero':
      return `${baseUrl}/seed/hero-banner/${width}/${height}`;
    
    case 'avatar':
      return `${baseUrl}/seed/avatar-${id || 'default'}/${width}/${height}`;
    
    default:
      return `${baseUrl}/${width}/${height}`;
  }
};

// 生成SVG模糊占位符
const generateBlurDataURL = (width: number = 400, height: number = 400): string => {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

export default function OptimizedImage({
  src,
  alt,
  width = 400,
  height = 400,
  fill = false,
  className = '',
  sizes,
  quality = 75,
  priority = false,
  loading = 'lazy',
  placeholder = 'blur',
  fallbackType = 'product',
  fallbackId,
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  // 确定要使用的图片URL
  const imageUrl = imageError || !src
    ? generatePlaceholderUrl(fallbackType, width, height, fallbackId)
    : src;

  // 处理图片加载错误
  const handleError = () => {
    if (retryCount < 2 && src) {
      // 重试加载原图片
      setRetryCount(prev => prev + 1);
      setTimeout(() => {
        setIsLoading(true);
      }, 1000 * retryCount);
    } else {
      setImageError(true);
      setIsLoading(false);
    }
  };

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    setImageError(false);
    setRetryCount(0);
  };

  // 生成模糊占位符数据URL
  const blurDataURL = placeholder === 'blur'
    ? generateBlurDataURL(width, height)
    : undefined;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* 加载状态指示器 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div>
        </div>
      )}
      
      {/* 主图片 */}
      <Image
        src={imageUrl}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        className={`transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'} ${className}`}
        sizes={sizes}
        quality={quality}
        priority={priority}
        loading={loading}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        onError={handleError}
        onLoad={handleLoad}
      />
      
      {/* 错误状态显示 */}
      {imageError && (
        <div className="absolute inset-0 bg-gray-100 flex flex-col items-center justify-center text-gray-500">
          <svg className="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span className="text-xs">图片加载失败</span>
        </div>
      )}
    </div>
  );
}
