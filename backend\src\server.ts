import express, { Request, Response } from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';

// 路由
import authRoutes from './routes/authRoutes';
// 这里可以导入其他路由

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// 创建Express应用
const app = express();

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// 路由
app.use('/api/auth', authRoutes);
// 这里可以添加其他路由

// 根路由
app.get('/', (req: Request, res: Response) => {
  res.send('时尚服饰电商API正在运行');
});

// 连接MongoDB
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/fashion-ecommerce';
mongoose
  .connect(MONGO_URI)
  .then(() => {
    console.log('MongoDB连接成功');
  })
  .catch((err) => {
    console.error('MongoDB连接失败:', err.message);
  });

// 启动服务器
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`服务器运行在端口: ${PORT}`);
}); 