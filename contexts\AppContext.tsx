'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// 定义状态类型
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  isAdmin: boolean;
}

export interface CartItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
  size?: string;
  color?: string;
}

export interface AppState {
  // 用户状态
  user: User | null;
  isLoggedIn: boolean;
  
  // 购物车状态
  cartItems: CartItem[];
  cartCount: number;
  
  // UI状态
  isMobileMenuOpen: boolean;
  isLoading: boolean;
  error: string | null;
}

// 定义动作类型
export type AppAction =
  | { type: 'SET_USER'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'ADD_TO_CART'; payload: CartItem }
  | { type: 'REMOVE_FROM_CART'; payload: number }
  | { type: 'UPDATE_CART_QUANTITY'; payload: { id: number; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'TOGGLE_MOBILE_MENU' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// 初始状态
const initialState: AppState = {
  user: null,
  isLoggedIn: false,
  cartItems: [],
  cartCount: 0,
  isMobileMenuOpen: false,
  isLoading: false,
  error: null,
};

// Reducer函数
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isLoggedIn: true,
        error: null,
      };
      
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isLoggedIn: false,
        cartItems: [],
        cartCount: 0,
      };
      
    case 'ADD_TO_CART': {
      const existingItem = state.cartItems.find(item => 
        item.id === action.payload.id && 
        item.size === action.payload.size && 
        item.color === action.payload.color
      );
      
      let newCartItems;
      if (existingItem) {
        newCartItems = state.cartItems.map(item =>
          item.id === action.payload.id && 
          item.size === action.payload.size && 
          item.color === action.payload.color
            ? { ...item, quantity: item.quantity + action.payload.quantity }
            : item
        );
      } else {
        newCartItems = [...state.cartItems, action.payload];
      }
      
      return {
        ...state,
        cartItems: newCartItems,
        cartCount: newCartItems.reduce((total, item) => total + item.quantity, 0),
      };
    }
    
    case 'REMOVE_FROM_CART': {
      const newCartItems = state.cartItems.filter(item => item.id !== action.payload);
      return {
        ...state,
        cartItems: newCartItems,
        cartCount: newCartItems.reduce((total, item) => total + item.quantity, 0),
      };
    }
    
    case 'UPDATE_CART_QUANTITY': {
      const newCartItems = state.cartItems.map(item =>
        item.id === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      ).filter(item => item.quantity > 0);
      
      return {
        ...state,
        cartItems: newCartItems,
        cartCount: newCartItems.reduce((total, item) => total + item.quantity, 0),
      };
    }
    
    case 'CLEAR_CART':
      return {
        ...state,
        cartItems: [],
        cartCount: 0,
      };
      
    case 'TOGGLE_MOBILE_MENU':
      return {
        ...state,
        isMobileMenuOpen: !state.isMobileMenuOpen,
      };
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
      
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
      
    default:
      return state;
  }
}

// 创建Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider组件
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// 自定义Hook
export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

// 便捷的Hook
export function useAuth() {
  const { state, dispatch } = useAppContext();
  
  const login = (user: User) => {
    dispatch({ type: 'SET_USER', payload: user });
  };
  
  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };
  
  return {
    user: state.user,
    isLoggedIn: state.isLoggedIn,
    login,
    logout,
  };
}

export function useCart() {
  const { state, dispatch } = useAppContext();
  
  const addToCart = (item: CartItem) => {
    dispatch({ type: 'ADD_TO_CART', payload: item });
  };
  
  const removeFromCart = (id: number) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: id });
  };
  
  const updateQuantity = (id: number, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_QUANTITY', payload: { id, quantity } });
  };
  
  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };
  
  return {
    cartItems: state.cartItems,
    cartCount: state.cartCount,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
  };
}

export function useUI() {
  const { state, dispatch } = useAppContext();
  
  const toggleMobileMenu = () => {
    dispatch({ type: 'TOGGLE_MOBILE_MENU' });
  };
  
  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };
  
  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };
  
  return {
    isMobileMenuOpen: state.isMobileMenuOpen,
    isLoading: state.isLoading,
    error: state.error,
    toggleMobileMenu,
    setLoading,
    setError,
  };
}
