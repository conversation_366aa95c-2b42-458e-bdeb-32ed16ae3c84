import Link from 'next/link';

export default function Header() {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="text-xl font-bold text-primary-500">
            时尚电商
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-primary-500">
              首页
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-primary-500">
              全部商品
            </Link>
            <Link href="/category/women" className="text-gray-700 hover:text-primary-500">
              女装
            </Link>
            <Link href="/category/men" className="text-gray-700 hover:text-primary-500">
              男装
            </Link>
            <Link href="/category/shoes" className="text-gray-700 hover:text-primary-500">
              鞋类
            </Link>
          </nav>

          {/* Cart */}
          <Link href="/cart" className="btn btn-primary">
            购物车
          </Link>
        </div>
      </div>
    </header>
  );
}
