import React from 'react';
import { FaBox, FaShoppingCart, FaUsers, FaChartLine, FaCog } from 'react-icons/fa';
import Link from 'next/link';

// 统计卡片组件
function StatCard({ title, value, icon, color, trend }: {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
  trend?: { value: string; isPositive: boolean };
}) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`p-3 rounded-lg ${color} text-white mr-4`}>
            {icon}
          </div>
          <div>
            <p className="text-gray-600 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trend && (
              <p className={`text-xs font-medium ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {trend.isPositive ? '↗' : '↘'} {trend.value}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// 最近订单项组件
function RecentOrderItem({ id, customer, date, total, status }: { id: string; customer: string; date: string; total: string; status: string }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成':
        return 'bg-green-100 text-green-800';
      case '处理中':
        return 'bg-blue-100 text-blue-800';
      case '已取消':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <tr className="border-b border-gray-200 hover:bg-gray-50">
      <td className="py-3 px-4">#{id}</td>
      <td className="py-3 px-4">{customer}</td>
      <td className="py-3 px-4">{date}</td>
      <td className="py-3 px-4">{total}</td>
      <td className="py-3 px-4">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
          {status}
        </span>
      </td>
      <td className="py-3 px-4">
        <Link href={`/admin/orders/${id}`} className="text-primary hover:underline">
          查看详情
        </Link>
      </td>
    </tr>
  );
}

// 最近活动项组件
function ActivityItem({ message, time, type }: { message: string; time: string; type: string }) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <FaShoppingCart className="text-blue-500" />;
      case 'product':
        return <FaBox className="text-green-500" />;
      case 'user':
        return <FaUsers className="text-purple-500" />;
      default:
        return <FaChartLine className="text-gray-500" />;
    }
  };

  return (
    <div className="flex items-start mb-4">
      <div className="mr-3 mt-1">{getTypeIcon(type)}</div>
      <div>
        <p className="text-sm">{message}</p>
        <p className="text-xs text-gray-500">{time}</p>
      </div>
    </div>
  );
}

// 管理后台首页
export default function AdminPage() {
  // 注意：在生产环境中应该添加适当的认证检查
  
  // 模拟数据
  const stats = [
    {
      title: '总订单数',
      value: '1,254',
      icon: <FaShoppingCart />,
      color: 'bg-blue-500',
      trend: { value: '+12.5%', isPositive: true }
    },
    {
      title: '总产品数',
      value: '452',
      icon: <FaBox />,
      color: 'bg-green-500',
      trend: { value: '+8.2%', isPositive: true }
    },
    {
      title: '注册用户',
      value: '2,815',
      icon: <FaUsers />,
      color: 'bg-purple-500',
      trend: { value: '+15.3%', isPositive: true }
    },
    {
      title: '总销售额',
      value: '¥152,485',
      icon: <FaChartLine />,
      color: 'bg-red-500',
      trend: { value: '+23.1%', isPositive: true }
    },
  ];

  const recentOrders = [
    { id: '12345', customer: '张三', date: '2023-06-21', total: '¥299.00', status: '已完成' },
    { id: '12344', customer: '李四', date: '2023-06-20', total: '¥599.00', status: '处理中' },
    { id: '12343', customer: '王五', date: '2023-06-19', total: '¥199.00', status: '已完成' },
    { id: '12342', customer: '赵六', date: '2023-06-18', total: '¥399.00', status: '已取消' },
    { id: '12341', customer: '钱七', date: '2023-06-17', total: '¥499.00', status: '已完成' },
  ];

  const recentActivities = [
    { message: '新订单 #12345 已创建', time: '10分钟前', type: 'order' },
    { message: '产品"夏季轻薄连衣裙"库存已更新', time: '1小时前', type: 'product' },
    { message: '新用户张三已注册', time: '3小时前', type: 'user' },
    { message: '产品"男士休闲西装外套"已添加', time: '5小时前', type: 'product' },
    { message: '订单 #12340 状态已更新为"已完成"', time: '1天前', type: 'order' },
  ];

  return (
    <div className="p-6 sm:p-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">管理控制台</h1>
        <p className="text-gray-600 mt-2">欢迎回来，这里是您的业务概览</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
          />
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 最近订单 */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="font-bold text-xl text-gray-900">最近订单</h2>
                <Link href="/admin/orders" className="text-primary hover:text-primary-dark text-sm font-medium">
                  查看全部 →
                </Link>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr className="text-left text-gray-600">
                    <th className="py-4 px-6 font-semibold">订单号</th>
                    <th className="py-4 px-6 font-semibold">客户</th>
                    <th className="py-4 px-6 font-semibold">日期</th>
                    <th className="py-4 px-6 font-semibold">金额</th>
                    <th className="py-4 px-6 font-semibold">状态</th>
                    <th className="py-4 px-6 font-semibold">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {recentOrders.map((order) => (
                    <RecentOrderItem
                      key={order.id}
                      id={order.id}
                      customer={order.customer}
                      date={order.date}
                      total={order.total}
                      status={order.status}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* 最近活动 */}
        <div>
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="font-bold text-xl text-gray-900">最近活动</h2>
                <Link href="/admin/activities" className="text-primary hover:text-primary-dark text-sm font-medium">
                  查看全部 →
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <ActivityItem
                    key={index}
                    message={activity.message}
                    time={activity.time}
                    type={activity.type}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="mt-8 bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="font-bold text-xl text-gray-900 mb-6">快速操作</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/admin/products/new" className="btn btn-primary flex items-center justify-center">
            <FaBox className="mr-2" />
            添加产品
          </Link>
          <Link href="/admin/orders/new" className="btn bg-green-500 text-white hover:bg-green-600 flex items-center justify-center">
            <FaShoppingCart className="mr-2" />
            创建订单
          </Link>
          <Link href="/admin/users/new" className="btn bg-purple-500 text-white hover:bg-purple-600 flex items-center justify-center">
            <FaUsers className="mr-2" />
            添加用户
          </Link>
          <Link href="/admin/settings" className="btn bg-gray-500 text-white hover:bg-gray-600 flex items-center justify-center">
            <FaCog className="mr-2" />
            系统设置
          </Link>
        </div>
      </div>
    </div>
  );
} 