'use client';

import Link from 'next/link';
import { useAuth, useCart, useUI } from '@/contexts/AppContext';

export default function Header() {
  const { user, isLoggedIn, logout } = useAuth();
  const { cartCount } = useCart();
  const { isMobileMenuOpen, toggleMobileMenu } = useUI();

  const navigationItems = [
    { name: '首页', href: '/' },
    { name: '女装', href: '/products/women' },
    { name: '男装', href: '/products/men' },
    { name: '新品', href: '/products/new' },
    { name: '全部商品', href: '/catalog' },
  ];

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* 左侧：品牌logo和导航 */}
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-primary flex-shrink-0">
              时尚电商
            </Link>
            
            {/* 桌面端导航 */}
            <nav className="hidden md:flex ml-8 space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium"
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>

          {/* 右侧：搜索、用户菜单和购物车 */}
          <div className="flex items-center space-x-4">
            {/* 搜索框 */}
            <div className="hidden lg:block">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* 用户菜单 */}
            <div className="flex items-center space-x-2">
              {isLoggedIn ? (
                <>
                  <Link href="/account" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2">
                    <span className="hidden lg:inline font-medium">我的账户</span>
                    <span className="lg:hidden">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </span>
                  </Link>
                  {user?.isAdmin && (
                    <Link href="/admin" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2">
                      <span className="hidden lg:inline font-medium">管理后台</span>
                      <span className="lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </span>
                    </Link>
                  )}
                  <button
                    onClick={logout}
                    className="text-gray-600 hover:text-primary transition-colors duration-200 p-2 hidden sm:block font-medium"
                  >
                    退出
                  </button>
                </>
              ) : (
                <>
                  <Link href="/login" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2 font-medium hidden sm:block">
                    登录
                  </Link>
                  <Link href="/register" className="btn btn-primary btn-sm hidden sm:inline-flex">
                    注册
                  </Link>
                </>
              )}
            </div>

            {/* 购物车 */}
            <Link href="/cart" className="relative p-2 text-gray-600 hover:text-primary transition-colors duration-200">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-pink-600 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center font-medium">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </Link>

            {/* 移动端菜单按钮 */}
            <button
              onClick={toggleMobileMenu}
              className="md:hidden p-2 text-gray-600 hover:text-primary transition-colors duration-200"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* 移动端导航菜单 */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="flex flex-col space-y-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium"
                  onClick={toggleMobileMenu}
                >
                  {item.name}
                </Link>
              ))}
              
              {/* 移动端搜索 */}
              <div className="pt-4 border-t border-gray-200">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              {/* 移动端用户操作 */}
              {!isLoggedIn && (
                <div className="pt-4 border-t border-gray-200 flex space-x-4">
                  <Link href="/login" className="text-gray-600 hover:text-primary font-medium">
                    登录
                  </Link>
                  <Link href="/register" className="btn btn-primary btn-sm">
                    注册
                  </Link>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
