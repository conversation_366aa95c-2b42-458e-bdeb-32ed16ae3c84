import CredentialsProvider from 'next-auth/providers/credentials';
import { NextAuthOptions } from 'next-auth';

// 扩展Session类型
declare module 'next-auth' {
  interface Session {
    accessToken?: string;
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
    }
  }
  
  interface User {
    id: string;
    name: string;
    email: string;
    image?: string | null;
    token: string;
    role: string;
  }
}

// 扩展JWT类型
declare module 'next-auth/jwt' {
  interface JWT {
    id?: string;
    role?: string;
    accessToken?: string;
  }
}

// NextAuth配置
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: '用户名', type: 'text', placeholder: '请输入用户名' },
        password: { label: '密码', type: 'password', placeholder: '请输入密码' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }
        
        // 临时登录验证，只接受admin/123456
        if (credentials.username === 'admin' && credentials.password === '123456') {
          return {
            id: '1',
            name: 'Administrator',
            email: '<EMAIL>',
            image: null,
            token: 'mock-token',
            role: 'admin',
          };
        }
        
        return null;
      },
    }),
  ],
  callbacks: {
    // 将令牌添加到会话中
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.accessToken = user.token;
      }
      return token;
    },
    // 将会话信息传递给客户端
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.accessToken = token.accessToken;
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30天
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-at-least-32-characters',
}; 