# 时尚服饰电商网站

这是一个基于Next.js的现代化电子商务网站，提供完整的购物体验和响应式设计。

## 功能特点

- 🎨 现代化响应式设计，完美适配各种设备
- 🛍️ 完整的产品展示和分类浏览系统
- 🛒 购物车功能（前端实现）
- 👤 用户注册和登录系统
- 📱 移动端优化的用户体验
- ⚡ 高性能静态数据加载
- 🎯 SEO友好的页面结构

## 技术栈

- **前端框架**: Next.js 15, React 19, TypeScript
- **样式**: Tailwind CSS 4
- **状态管理**: Zustand
- **表单处理**: Formik + Yup
- **认证**: NextAuth.js
- **图标**: React Icons
- **后端**: Express.js (可选)

## 快速开始

### 前提条件

- Node.js 18+
- npm 或 yarn

### 安装步骤

1. 克隆仓库
   ```bash
   git clone https://github.com/yourusername/fashion-ecommerce.git
   cd fashion-ecommerce
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 启动开发服务器
   ```bash
   npm run dev
   ```

4. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 项目结构

```
/
├── app/                  # Next.js应用目录
│   ├── admin/            # 管理后台页面
│   ├── api/              # API路由
│   ├── catalog/          # 产品目录页面
│   ├── products/         # 产品详情页面
│   ├── login/            # 登录页面
│   ├── register/         # 注册页面
│   └── layout.tsx        # 根布局
├── backend/              # 后端服务器（可选）
│   └── src/
├── public/               # 静态资源
│   └── images/           # 图片资源
├── utils/                # 工具函数
│   ├── mockData.ts       # 静态模拟数据
│   └── wordpress.ts      # 数据获取工具
└── frontend/             # 前端组件（可选）
    └── src/
```

## 主要页面

- **首页** (`/`) - 展示精选产品和分类
- **产品目录** (`/catalog`) - 所有产品列表
- **分类页面** (`/catalog/[category]`) - 按分类浏览产品
- **产品详情** (`/products/detail/[id]`) - 产品详细信息
- **登录页面** (`/login`) - 用户登录
- **注册页面** (`/register`) - 用户注册
- **管理后台** (`/admin`) - 产品管理界面

## 数据管理

项目使用静态模拟数据，所有产品信息存储在 `utils/mockData.ts` 文件中：

- 产品数据包含：名称、价格、图片、分类等
- 分类数据包含：名称、描述、图片等
- 支持特价商品显示
- 图片使用Next.js Image组件优化

## 性能优化

- 🖼️ 图片懒加载和优化
- ⚡ 静态页面生成
- 🔄 智能缓存策略
- 📦 代码分割和按需加载
- 🎯 SEO优化

## 自定义配置

### 添加新产品

在 `utils/mockData.ts` 中的 `FALLBACK_PRODUCTS` 数组添加新产品：

```typescript
{
  id: 产品ID,
  name: "产品名称",
  slug: "产品别名",
  price: "价格",
  regular_price: "原价",
  sale_price: "特价",
  on_sale: false,
  images: [
    {
      id: 1,
      src: "/images/product1.jpg",
      name: "产品图片",
      alt: "产品描述"
    }
  ]
}
```

### 添加新分类

在 `FALLBACK_CATEGORIES` 数组中添加新分类：

```typescript
{
  id: 分类ID,
  name: "分类名称",
  slug: "分类别名",
  count: 产品数量,
  description: "分类描述",
  image: {
    id: 1,
    src: "/images/category-image.jpg",
    alt: "分类图片"
  }
}
```

## 部署

### Vercel部署（推荐）

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 自动部署完成

### 其他平台

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 开发脚本

```bash
# 开发模式
npm run dev

# 构建项目
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 启动后端服务器（可选）
npm run server
```

## 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/yourusername/fashion-ecommerce/issues)
- 邮箱: <EMAIL>

---

**注意**: 这是一个演示项目，使用静态模拟数据。在实际生产环境中，您需要集成真实的后端API和数据库。
