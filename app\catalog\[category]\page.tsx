import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { FALLBACK_PRODUCTS, FALLBACK_CATEGORIES } from '@/utils/mockData';
import { IMAGES, IMAGE_SIZES, getCategoryBanner } from '@/utils/imageConstants';
import OptimizedImage from '@/components/ui/OptimizedImage';

// 定义类型
interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: {
    id: number;
    src: string;
    alt: string;
  };
}

interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface Product {
  id: number;
  name: string;
  slug: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  images: ProductImage[];
  stock_status?: string;
  categories: { id: number; name: string; slug: string }[];
}

// 分类页面
// @ts-expect-error Next.js 15 路由类型推断问题，实际类型安全
export default async function CategoryPage({ params }: { params: Promise<{ category: string }> }) {
  // 等待params解析
  const { category } = await params;

  // 从静态数据中获取当前分类
  const currentCategory = FALLBACK_CATEGORIES.find((cat) => cat.slug === category);
  
  // 如果找不到分类，返回404
  if (!currentCategory) {
    notFound();
  }
  
  // 从静态数据中获取该分类下的产品
  const products = FALLBACK_PRODUCTS.filter(product => 
    product.categories.some(cat => cat.id === currentCategory.id)
  );
  
  return (
    <div>
      {/* 分类横幅 */}
      <div className="relative h-[300px] mb-8">
        <OptimizedImage
          src={getCategoryBanner(currentCategory.slug)}
          alt={currentCategory.name}
          fill
          sizes={IMAGE_SIZES.category.banner}
          quality={80}
          priority
          loading="eager"
          className="object-cover"
          fallbackType="category"
          fallbackId={currentCategory.id}
        />
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <h1 className="text-4xl font-bold text-white">{currentCategory.name}</h1>
        </div>
      </div>
      
      {/* 分类描述 */}
      {currentCategory.description && (
        <div className="container mx-auto px-4 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: currentCategory.description }} />
          </div>
        </div>
      )}
      
      {/* 产品过滤和排序 */}
      <div className="container mx-auto px-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow-sm flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <span className="text-gray-600">显示 {products.length} 个产品</span>
          </div>
          <div className="flex flex-wrap gap-4">
            <select className="form-control max-w-xs">
              <option value="default">默认排序</option>
              <option value="price-asc">价格从低到高</option>
              <option value="price-desc">价格从高到低</option>
              <option value="name-asc">名称 A-Z</option>
              <option value="name-desc">名称 Z-A</option>
              <option value="newest">最新产品</option>
            </select>
            <select className="form-control max-w-xs">
              <option value="all">所有状态</option>
              <option value="instock">有库存</option>
              <option value="outofstock">缺货</option>
              <option value="onsale">特价</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 产品列表 */}
      <div className="container mx-auto px-4 mb-16">
        {products.length === 0 ? (
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold mb-4">没有找到产品</h2>
            <p className="text-gray-600 mb-8">该分类下暂时没有产品，请稍后再查看。</p>
            <Link href="/products" className="btn btn-primary bg-primary text-white px-6 py-2 rounded">
              查看所有产品
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product: Product) => (
              <div key={product.id} className="product-card group">
                <div className="product-image relative h-[300px] overflow-hidden rounded-t-lg">
                  {product.on_sale && (
                    <div className="product-badge absolute top-2 right-2 z-10">
                      <span className="badge badge-danger bg-red-500 text-white px-2 py-1 text-xs rounded">特价</span>
                    </div>
                  )}
                  {product.stock_status === 'outofstock' && (
                    <div className="product-badge absolute top-2 left-2 z-10">
                      <span className="badge badge-warning bg-yellow-500 text-white px-2 py-1 text-xs rounded">缺货</span>
                    </div>
                  )}
                  <Link href={`/products/detail/${product.id}`}>
                    <OptimizedImage
                      src={product.images && product.images.length > 0 ? product.images[0].src : undefined}
                      alt={product.name}
                      fill
                      sizes={IMAGE_SIZES.product.card}
                      quality={75}
                      loading="lazy"
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                      fallbackType="product"
                      fallbackId={product.id}
                    />
                  </Link>
                </div>
                <div className="product-info p-4 bg-white rounded-b-lg">
                  <Link href={`/products/detail/${product.id}`} className="product-name text-lg font-medium hover:text-primary">
                    {product.name}
                  </Link>
                  <div className="flex items-center mt-2">
                    {product.on_sale ? (
                      <>
                        <span className="product-price text-primary font-bold">¥{product.sale_price}</span>
                        <span className="product-original-price text-gray-400 line-through ml-2 text-sm">¥{product.regular_price}</span>
                      </>
                    ) : (
                      <span className="product-price text-primary font-bold">¥{product.price}</span>
                    )}
                  </div>
                  <button 
                    className={`mt-4 w-full btn ${
                      product.stock_status === 'outofstock' 
                        ? 'bg-gray-300 text-gray-600 cursor-not-allowed' 
                        : 'btn-primary bg-primary text-white transition-opacity opacity-80 group-hover:opacity-100'
                    }`}
                    disabled={product.stock_status === 'outofstock'}
                  >
                    {product.stock_status === 'outofstock' ? '缺货' : '加入购物车'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// 生成静态路径
// @ts-expect-error Next.js 15 路由类型推断问题，实际类型安全
export function generateStaticParams() {
  return FALLBACK_CATEGORIES.map((category) => ({
    category: category.slug,
  }));
} 