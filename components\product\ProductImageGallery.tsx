'use client';

import React, { useState } from 'react';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { IMAGES } from '@/utils/imageConstants';

interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface ProductImageGalleryProps {
  images: ProductImage[];
  productName: string;
  productId: number;
  onSale?: boolean;
  stockStatus?: string;
}

export default function ProductImageGallery({ 
  images, 
  productName, 
  productId, 
  onSale, 
  stockStatus 
}: ProductImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  // 默认选择第一张图片
  const mainImage = images && images.length > 0
    ? images[selectedImageIndex].src
    : IMAGES.product(productId);

  return (
    <div className="space-y-4">
      <div className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
        <OptimizedImage
          src={mainImage}
          alt={productName}
          fill
          sizes="(max-width: 1024px) 100vw, 50vw"
          quality={85}
          priority
          className="object-cover"
          fallbackType="product"
          fallbackId={productId}
        />
        {onSale && (
          <div className="absolute top-4 left-4">
            <span className="badge badge-danger bg-red-500 text-white px-2 py-1 text-xs rounded">特价</span>
          </div>
        )}
        {stockStatus === 'outofstock' && (
          <div className="absolute top-4 right-4">
            <span className="badge badge-warning bg-yellow-500 text-white px-2 py-1 text-xs rounded">缺货</span>
          </div>
        )}
      </div>
      
      {/* 缩略图 */}
      {images && images.length > 1 && (
        <div className="grid grid-cols-5 gap-2">
          {images.map((image, index) => (
            <div 
              key={index} 
              className={`relative aspect-square rounded-md overflow-hidden border cursor-pointer ${
                selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
              }`}
              onClick={() => setSelectedImageIndex(index)}
            >
              <OptimizedImage
                src={image.src}
                alt={image.name || `${productName} - 图片 ${index + 1}`}
                fill
                sizes="20vw"
                quality={75}
                loading="lazy"
                className="object-cover hover:scale-105 transition-transform"
                fallbackType="product"
                fallbackId={productId}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
