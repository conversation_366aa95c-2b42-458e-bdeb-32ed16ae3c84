import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const RegisterForm: React.FC = () => {
  const formik = useFormik({
    initialValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
    validationSchema: Yup.object({
      username: Yup.string()
        .required('用户名不能为空')
        .min(3, '用户名至少需要3个字符'),
      email: Yup.string()
        .email('邮箱格式不正确')
        .required('邮箱不能为空'),
      password: Yup.string()
        .required('密码不能为空')
        .min(8, '密码至少需要8个字符')
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
          '密码需包含至少一个大写字母、一个小写字母和一个数字'
        ),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], '两次输入的密码不一致')
        .required('请确认密码'),
    }),
    onSubmit: (values) => {
      console.log('注册表单提交:', values);
      // 这里将调用API进行注册
    },
  });

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">用户注册</h2>
      <form onSubmit={formik.handleSubmit}>
        <div className="form-control">
          <label htmlFor="username">用户名</label>
          <input
            id="username"
            name="username"
            type="text"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.username}
          />
          {formik.touched.username && formik.errors.username ? (
            <div className="text-error text-sm mt-1">{formik.errors.username}</div>
          ) : null}
        </div>

        <div className="form-control">
          <label htmlFor="email">邮箱</label>
          <input
            id="email"
            name="email"
            type="email"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.email}
          />
          {formik.touched.email && formik.errors.email ? (
            <div className="text-error text-sm mt-1">{formik.errors.email}</div>
          ) : null}
        </div>

        <div className="form-control">
          <label htmlFor="password">密码</label>
          <input
            id="password"
            name="password"
            type="password"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.password}
          />
          {formik.touched.password && formik.errors.password ? (
            <div className="text-error text-sm mt-1">{formik.errors.password}</div>
          ) : null}
        </div>

        <div className="form-control">
          <label htmlFor="confirmPassword">确认密码</label>
          <input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.confirmPassword}
          />
          {formik.touched.confirmPassword && formik.errors.confirmPassword ? (
            <div className="text-error text-sm mt-1">{formik.errors.confirmPassword}</div>
          ) : null}
        </div>

        <div className="mt-6">
          <button
            type="submit"
            className="w-full btn btn-primary"
            disabled={formik.isSubmitting}
          >
            {formik.isSubmitting ? '注册中...' : '注册'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RegisterForm; 