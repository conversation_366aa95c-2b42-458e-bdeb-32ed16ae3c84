import { notFound } from 'next/navigation';
import ProductCard from '@/components/ProductCard';
import { getProductsByCategory, getCategories } from '@/lib/data';

interface CategoryPageProps {
  params: Promise<{ id: string }>;
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { id } = await params;
  const products = getProductsByCategory(id);
  const categories = getCategories();
  const category = categories.find(c => c.id === id);

  if (!category) {
    notFound();
  }

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">{category.name}</h1>
      
      {products.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">该分类暂无商品</p>
        </div>
      )}
    </div>
  );
}
