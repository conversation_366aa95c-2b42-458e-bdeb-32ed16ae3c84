{"name": "fashion-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "server": "ts-node backend/src/server.ts", "server:simple": "ts-node backend/src/simple-server.ts", "dev:server": "nodemon --exec ts-node backend/src/server.ts"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.16", "@wordpress/api-fetch": "^7.25.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "formik": "^2.4.5", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "next": "15.3.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^4.12.0", "yup": "^1.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "nodemon": "^3.0.2", "tailwindcss": "^4", "typescript": "^5"}}