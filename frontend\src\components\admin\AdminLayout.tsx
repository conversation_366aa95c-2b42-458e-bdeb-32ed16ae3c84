import React from 'react';
import Sidebar from './Sidebar';
import { FaBell, FaUser } from 'react-icons/fa';

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
  activePage: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title, activePage }) => {
  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* 侧边栏 */}
      <Sidebar active={activePage} />

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部导航栏 */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-6 py-4 flex items-center justify-between">
            <h1 className="text-2xl font-bold">{title}</h1>
            
            <div className="flex items-center space-x-4">
              {/* 通知图标 */}
              <button className="text-gray-500 hover:text-gray-700 relative">
                <FaBell size={20} />
                <span className="absolute -top-1 -right-1 bg-secondary text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
                  3
                </span>
              </button>
              
              {/* 用户菜单 */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <FaUser className="text-gray-600" />
                </div>
                <span className="font-medium">管理员</span>
              </div>
            </div>
          </div>
        </header>
        
        {/* 页面内容 */}
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout; 