'use client';

import React from 'react';
import Link from 'next/link';
import OptimizedImage from './OptimizedImage';
import Button from './Button';
import { useCart } from '@/contexts/AppContext';
import { IMAGES } from '@/utils/imageConstants';

interface Product {
  id: number;
  name: string;
  price: string;
  regular_price?: string;
  sale_price?: string;
  on_sale?: boolean;
  images?: Array<{ id: number; src: string; name: string; alt: string }>;
  stock_status?: string;
}

interface ProductCardProps {
  product: Product;
  className?: string;
}

const ProductCard = React.memo(function ProductCard({ product, className = '' }: ProductCardProps) {
  const { addToCart } = useCart();

  const handleAddToCart = () => {
    addToCart({
      id: product.id,
      name: product.name,
      price: parseFloat(product.sale_price || product.price),
      quantity: 1,
      image: product.images?.[0]?.src || IMAGES.product(product.id),
    });
  };

  const displayPrice = product.on_sale && product.sale_price ? product.sale_price : product.price;
  const originalPrice = product.on_sale && product.regular_price ? product.regular_price : null;

  return (
    <div className={`product-card group ${className}`}>
      <div className="product-image relative h-[300px] overflow-hidden rounded-t-lg">
        {product.on_sale && (
          <div className="product-badge absolute top-2 right-2 z-10">
            <span className="badge badge-danger">特价</span>
          </div>
        )}
        
        <Link href={`/products/detail/${product.id}`}>
          <OptimizedImage
            src={product.images?.[0]?.src || IMAGES.product(product.id)}
            alt={product.name}
            fill
            className="product-image-img"
            fallbackType="product"
            fallbackId={product.id}
          />
        </Link>
        
        {/* 悬停时显示的快速操作 */}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="flex space-x-2">
            <Link href={`/products/detail/${product.id}`}>
              <Button variant="outline" size="sm" className="bg-white text-gray-900 hover:bg-gray-100">
                查看详情
              </Button>
            </Link>
            <Button 
              variant="primary" 
              size="sm"
              onClick={handleAddToCart}
              disabled={product.stock_status === 'outofstock'}
            >
              加入购物车
            </Button>
          </div>
        </div>
      </div>
      
      <div className="product-info p-4">
        <Link href={`/products/detail/${product.id}`}>
          <h3 className="product-title text-lg font-semibold text-gray-900 hover:text-primary transition-colors duration-200 line-clamp-2">
            {product.name}
          </h3>
        </Link>
        
        <div className="product-price mt-2 flex items-center space-x-2">
          <span className="text-xl font-bold text-primary">
            ¥{displayPrice}
          </span>
          {originalPrice && (
            <span className="text-sm text-gray-500 line-through">
              ¥{originalPrice}
            </span>
          )}
          {product.on_sale && originalPrice && (
            <span className="text-sm font-medium text-green-600">
              省¥{(parseFloat(originalPrice) - parseFloat(displayPrice)).toFixed(0)}
            </span>
          )}
        </div>
        
        {product.stock_status === 'outofstock' && (
          <div className="mt-2">
            <span className="text-sm text-red-600 font-medium">暂时缺货</span>
          </div>
        )}
      </div>
    </div>
  );
});

export default ProductCard;
