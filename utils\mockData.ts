// 模拟数据 - 产品列表
export const FALLBACK_PRODUCTS = [
  {
    id: 1,
    name: '男士修身西装',
    slug: 'mens-slim-fit-suit',
    permalink: '/products/mens-slim-fit-suit',
    date_created: '2023-11-01T12:00:00',
    type: 'simple',
    status: 'publish',
    featured: true,
    catalog_visibility: 'visible',
    description: '高品质男士修身西装，适合商务场合和正式活动穿着。采用优质面料，舒适透气。',
    short_description: '高品质男士修身西装，适合商务场合。',
    price: '899.00',
    regular_price: '999.00',
    sale_price: '899.00',
    on_sale: true,
    purchasable: true,
    total_sales: 58,
    virtual: false,
    downloadable: false,
    categories: [
      {
        id: 15,
        name: '男装',
        slug: 'men'
      }
    ],
    tags: [
      {
        id: 34,
        name: '西装',
        slug: 'suits'
      },
      {
        id: 35,
        name: '商务',
        slug: 'business'
      }
    ],
    images: [
      {
        id: 101,
        src: '/images/product1.jpg',
        name: '男士修身西装',
        alt: '男士修身西装正面图'
      }
    ],
    attributes: [
      {
        id: 1,
        name: '尺寸',
        position: 0,
        visible: true,
        variation: true,
        options: ['S', 'M', 'L', 'XL', 'XXL']
      },
      {
        id: 2,
        name: '颜色',
        position: 1,
        visible: true,
        variation: true,
        options: ['黑色', '深蓝色', '灰色']
      }
    ],
    stock_quantity: 25,
    stock_status: 'instock',
  },
  {
    id: 2,
    name: '女士连衣裙',
    slug: 'womens-dress',
    permalink: '/products/womens-dress',
    date_created: '2023-11-05T10:30:00',
    type: 'simple',
    status: 'publish',
    featured: true,
    catalog_visibility: 'visible',
    description: '时尚女士连衣裙，采用优质面料，舒适透气，适合各种场合穿着。',
    short_description: '时尚女士连衣裙，舒适透气。',
    price: '459.00',
    regular_price: '599.00',
    sale_price: '459.00',
    on_sale: true,
    purchasable: true,
    total_sales: 124,
    virtual: false,
    downloadable: false,
    categories: [
      {
        id: 16,
        name: '女装',
        slug: 'women'
      }
    ],
    tags: [
      {
        id: 36,
        name: '连衣裙',
        slug: 'dress'
      },
      {
        id: 37,
        name: '时尚',
        slug: 'fashion'
      }
    ],
    images: [
      {
        id: 102,
        src: '/images/product2.jpg',
        name: '女士连衣裙',
        alt: '女士连衣裙正面图'
      }
    ],
    attributes: [
      {
        id: 1,
        name: '尺寸',
        position: 0,
        visible: true,
        variation: true,
        options: ['XS', 'S', 'M', 'L', 'XL']
      },
      {
        id: 2,
        name: '颜色',
        position: 1,
        visible: true,
        variation: true,
        options: ['红色', '蓝色', '黑色', '白色']
      }
    ],
    stock_quantity: 42,
    stock_status: 'instock',
  },
  {
    id: 3,
    name: '休闲运动鞋',
    slug: 'casual-sports-shoes',
    permalink: '/products/casual-sports-shoes',
    date_created: '2023-11-10T14:45:00',
    type: 'simple',
    status: 'publish',
    featured: false,
    catalog_visibility: 'visible',
    description: '舒适休闲运动鞋，采用轻质材料，适合日常穿着和轻度运动。',
    short_description: '舒适休闲运动鞋，轻质材料。',
    price: '329.00',
    regular_price: '399.00',
    sale_price: '329.00',
    on_sale: true,
    purchasable: true,
    total_sales: 87,
    virtual: false,
    downloadable: false,
    categories: [
      {
        id: 17,
        name: '鞋类',
        slug: 'shoes'
      }
    ],
    tags: [
      {
        id: 38,
        name: '运动',
        slug: 'sports'
      },
      {
        id: 39,
        name: '休闲',
        slug: 'casual'
      }
    ],
    images: [
      {
        id: 103,
        src: '/images/product3.jpg',
        name: '休闲运动鞋',
        alt: '休闲运动鞋侧面图'
      }
    ],
    attributes: [
      {
        id: 3,
        name: '尺码',
        position: 0,
        visible: true,
        variation: true,
        options: ['39', '40', '41', '42', '43', '44', '45']
      },
      {
        id: 2,
        name: '颜色',
        position: 1,
        visible: true,
        variation: true,
        options: ['黑色', '白色', '灰色', '蓝色']
      }
    ],
    stock_quantity: 35,
    stock_status: 'instock',
  },
  {
    id: 4,
    name: '时尚手提包',
    slug: 'fashion-handbag',
    permalink: '/products/fashion-handbag',
    date_created: '2023-11-15T09:15:00',
    type: 'simple',
    status: 'publish',
    featured: true,
    catalog_visibility: 'visible',
    description: '高品质时尚手提包，采用优质PU革材质，内部空间宽敞，设计精美。',
    short_description: '高品质时尚手提包，设计精美。',
    price: '259.00',
    regular_price: '299.00',
    sale_price: '259.00',
    on_sale: true,
    purchasable: true,
    total_sales: 64,
    virtual: false,
    downloadable: false,
    categories: [
      {
        id: 18,
        name: '配饰',
        slug: 'accessories'
      }
    ],
    tags: [
      {
        id: 40,
        name: '包包',
        slug: 'bags'
      },
      {
        id: 41,
        name: '时尚',
        slug: 'fashion'
      }
    ],
    images: [
      {
        id: 104,
        src: '/images/product4.jpg',
        name: '时尚手提包',
        alt: '时尚手提包正面图'
      }
    ],
    attributes: [
      {
        id: 2,
        name: '颜色',
        position: 0,
        visible: true,
        variation: true,
        options: ['黑色', '棕色', '红色', '米色']
      },
      {
        id: 4,
        name: '材质',
        position: 1,
        visible: true,
        variation: false,
        options: ['PU革', '真皮']
      }
    ],
    stock_quantity: 18,
    stock_status: 'instock',
  }
];

// 模拟数据 - 分类列表
export const FALLBACK_CATEGORIES = [
  {
    id: 1,
    name: '女装',
    slug: 'women',
    count: 12,
    description: '时尚女装系列，包含各种风格的服装，满足您的不同需求。',
    image: {
      id: 101,
      src: '/images/category-women.jpg',
      alt: '女装分类'
    }
  },
  {
    id: 2,
    name: '男装',
    slug: 'men',
    count: 8,
    description: '精选男装系列，展现男士魅力与品味。',
    image: {
      id: 102,
      src: '/images/category-men.jpg',
      alt: '男装分类'
    }
  },
  {
    id: 3,
    name: '鞋子',
    slug: 'shoes',
    count: 6,
    description: '各种风格的鞋子，舒适与时尚并重。',
    image: {
      id: 103,
      src: '/images/category-shoes.jpg',
      alt: '鞋子分类'
    }
  },
  {
    id: 4,
    name: '配饰',
    slug: 'accessories',
    count: 10,
    description: '精美配饰，为您的穿搭增添亮点。',
    image: {
      id: 104,
      src: '/images/category-accessories.jpg',
      alt: '配饰分类'
    }
  }
];

// 模拟数据 - 订单列表
export const FALLBACK_ORDERS = [
  {
    id: 1001,
    status: 'processing',
    date_created: '2023-12-01T10:25:36',
    total: '1358.00',
    customer_id: 501,
    billing: {
      first_name: '张',
      last_name: '三',
      email: '<EMAIL>',
      phone: '13800138001',
      address_1: '北京市朝阳区某街道100号',
      city: '北京市',
      state: '北京',
      postcode: '100000',
      country: 'CN'
    },
    line_items: [
      {
        id: 101,
        name: '男士修身西装',
        product_id: 1,
        quantity: 1,
        price: '899.00'
      },
      {
        id: 102,
        name: '时尚手提包',
        product_id: 4,
        quantity: 1,
        price: '259.00'
      }
    ]
  },
  {
    id: 1002,
    status: 'completed',
    date_created: '2023-11-28T14:52:11',
    total: '459.00',
    customer_id: 502,
    billing: {
      first_name: '李',
      last_name: '四',
      email: '<EMAIL>',
      phone: '13900139002',
      address_1: '上海市静安区某路200号',
      city: '上海市',
      state: '上海',
      postcode: '200000',
      country: 'CN'
    },
    line_items: [
      {
        id: 103,
        name: '女士连衣裙',
        product_id: 2,
        quantity: 1,
        price: '459.00'
      }
    ]
  }
]; 