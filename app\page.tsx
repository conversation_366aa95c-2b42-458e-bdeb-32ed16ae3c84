import Image from 'next/image';
import Link from 'next/link';
import ProductCard from '@/components/ProductCard';
import { getFeaturedProducts, getCategories } from '@/lib/data';

export default function HomePage() {
  const featuredProducts = getFeaturedProducts();
  const categories = getCategories();

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-96 bg-gradient-to-r from-primary-500 to-primary-700">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative container h-full flex items-center">
          <div className="text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              探索时尚
            </h1>
            <p className="text-xl mb-8">
              发现最新的时尚趋势，展现您的个性风格
            </p>
            <Link href="/products" className="btn bg-white text-primary-500 hover:bg-gray-100">
              立即购买
            </Link>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16">
        <div className="container">
          <h2 className="text-3xl font-bold text-center mb-12">热门分类</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.id}`}
                className="group"
              >
                <div className="card">
                  <div className="relative aspect-video overflow-hidden">
                    <Image
                      src={category.image}
                      alt={category.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    />
                  </div>
                  <div className="p-4 text-center">
                    <h3 className="font-semibold text-lg">{category.name}</h3>
                    <p className="text-gray-600">{category.productCount} 件商品</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl font-bold text-center mb-12">精选商品</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/products" className="btn btn-outline">
              查看全部商品
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
