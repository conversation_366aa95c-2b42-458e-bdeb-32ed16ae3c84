'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FALLBACK_PRODUCTS, FALLBACK_CATEGORIES } from '@/utils/mockData';
import { IMAGES, IMAGE_SIZES, getCategoryImage, smartPreloadImages } from '@/utils/imageConstants';
import OptimizedImage from '@/components/ui/OptimizedImage';

// 注意：revalidate 不能在客户端组件中使用

// 定义类型
interface Category {
  id: number;
  name: string;
  slug: string;
  count?: number;
  description?: string;
  image?: {
    id: number;
    src: string;
    alt: string;
  };
}

interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface Product {
  id: number;
  name: string;
  slug: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  images: ProductImage[];
}

export default function Home() {
  // 直接使用静态数据，避免API调用
  const featuredProducts = FALLBACK_PRODUCTS.slice(0, 4);
  const categories = FALLBACK_CATEGORIES;

  // 预加载关键图片
  useEffect(() => {
    const criticalImages = [
      IMAGES.hero.main,
      IMAGES.hero.fashion,
      ...categories.slice(0, 4).map(cat => getCategoryImage(cat.slug)),
      ...featuredProducts.map(product =>
        product.images && product.images.length > 0
          ? product.images[0].src
          : IMAGES.product(product.id)
      )
    ];

    // 延迟预加载非关键图片，使用智能预加载
    setTimeout(() => {
      smartPreloadImages(criticalImages);
    }, 100);
  }, [featuredProducts, categories]);
  
  return (
    <div>
      {/* 主横幅 */}
      <div className="relative">
        <div className="h-[400px] md:h-[500px] lg:h-[600px] relative overflow-hidden">
          <OptimizedImage
            src={IMAGES.hero.main}
            alt="时尚服饰"
            fill
            sizes={IMAGE_SIZES.hero}
            quality={80}
            priority
            loading="eager"
            className="object-cover"
            fallbackType="hero"
            fallbackId="main"
          />
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center text-center p-4">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">探索时尚的无限可能</h1>
              <p className="text-xl text-white mb-8">发现最新的时尚趋势，展现您的个性风格</p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/catalog/women" className="btn btn-primary px-8 py-3 text-lg">
                  女装系列
                </Link>
                <Link href="/catalog/men" className="btn bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 text-lg">
                  男装系列
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 特色分类 */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">浏览分类</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category: Category) => (
            <Link href={`/catalog/${category.slug}`} key={category.id} className="group">
              <div className="relative rounded-lg overflow-hidden h-64">
                <OptimizedImage
                  src={getCategoryImage(category.slug)}
                  alt={category.name}
                  fill
                  sizes={IMAGE_SIZES.category.card}
                  quality={75}
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  fallbackType="category"
                  fallbackId={category.id}
                />
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-500 group-hover:bg-black/50">
                  <h3 className="text-2xl font-bold text-white">{category.name}</h3>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* 精选产品 */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">精选产品</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product: Product) => (
              <div key={product.id} className="product-card">
                <div className="product-image">
                  <Link href={`/products/detail/${product.id}`}>
                    <OptimizedImage
                      src={product.images && product.images.length > 0 ? product.images[0].src : undefined}
                      alt={product.name}
                      fill
                      sizes={IMAGE_SIZES.product.card}
                      quality={75}
                      loading="lazy"
                      className="product-image-img"
                      fallbackType="product"
                      fallbackId={product.id}
                    />
                  </Link>

                  {/* 产品徽章 */}
                  <div className="product-badges">
                    {product.on_sale && (
                      <span className="badge badge-secondary">特价</span>
                    )}
                  </div>

                  {/* 快捷操作按钮 */}
                  <div className="product-actions">
                    <button className="product-action-btn" title="添加到收藏">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600 hover:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </button>
                    <button className="product-action-btn" title="快速查看">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600 hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </div>
                </div>

                <div className="product-info">
                  <div className="product-category">精选</div>
                  <Link href={`/products/detail/${product.id}`}>
                    <h3 className="product-name">{product.name}</h3>
                  </Link>
                  <div className="product-price-container">
                    <div className="flex items-center">
                      {product.on_sale ? (
                        <>
                          <span className="product-price">¥{product.sale_price}</span>
                          <span className="product-original-price">¥{product.regular_price}</span>
                        </>
                      ) : (
                        <span className="product-price">¥{product.price}</span>
                      )}
                    </div>
                    {product.on_sale && (
                      <span className="product-discount">
                        -{Math.round(((parseFloat(product.regular_price) - parseFloat(product.sale_price)) / parseFloat(product.regular_price)) * 100)}%
                      </span>
                    )}
                  </div>
                  <button className="product-add-to-cart">
                    加入购物车
                  </button>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/products" className="btn btn-outline border border-gray-400 px-6 py-2 rounded hover:border-primary hover:text-primary">
              查看全部产品
            </Link>
          </div>
        </div>
      </div>

      {/* 品牌故事 */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="relative h-[400px] rounded-lg overflow-hidden">
            <OptimizedImage
              src={IMAGES.hero.fashion}
              alt="品牌故事"
              fill
              sizes="(max-width: 1024px) 100vw, 50vw"
              quality={75}
              loading="lazy"
              className="object-cover"
              fallbackType="hero"
              fallbackId="brand-story"
            />
          </div>
          <div>
            <h2 className="text-3xl font-bold mb-6">我们的品牌故事</h2>
            <p className="text-lg text-gray-600 mb-4">
              我们的品牌创立于2010年，秉承"时尚源于生活"的理念，致力于为顾客提供高品质、舒适且富有设计感的服装产品。
            </p>
            <p className="text-lg text-gray-600 mb-6">
              我们相信每个人都应该拥有表达自我风格的权利，因此我们不断创新，将时尚与实用完美结合，让每一件服装都能讲述一个独特的故事。
            </p>
            <Link href="/about" className="btn btn-primary bg-primary text-white px-6 py-2 rounded">
              了解更多
            </Link>
          </div>
        </div>
      </div>

      {/* 特色服务 */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">我们的服务</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">24/7 客户支持</h3>
              <p className="text-gray-600">随时为您解答问题，提供专业的购物建议和售后服务。</p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">全球配送</h3>
              <p className="text-gray-600">快速高效的物流服务，让您在世界各地都能享受我们的产品。</p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">轻松退换</h3>
              <p className="text-gray-600">30天无理由退换，为您的购物提供更多保障和信心。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
