import Image from 'next/image';
import { notFound } from 'next/navigation';
import { getProduct } from '@/lib/data';

interface ProductPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { id } = await params;
  const product = getProduct(parseInt(id));

  if (!product) {
    notFound();
  }

  const hasDiscount = product.originalPrice && product.originalPrice > product.price;

  return (
    <div className="container py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Image */}
        <div className="relative aspect-square">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover rounded-lg"
            sizes="(max-width: 1024px) 100vw, 50vw"
            priority
          />
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-3xl font-bold mb-4">{product.name}</h1>
          
          <div className="flex items-center space-x-4 mb-6">
            <span className="text-3xl font-bold text-primary-500">
              ¥{product.price}
            </span>
            {hasDiscount && (
              <>
                <span className="text-xl text-gray-500 line-through">
                  ¥{product.originalPrice}
                </span>
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                  省 ¥{product.originalPrice! - product.price}
                </span>
              </>
            )}
          </div>

          <p className="text-gray-600 mb-8 leading-relaxed">
            {product.description}
          </p>

          <div className="mb-6">
            <span className={`inline-block px-3 py-1 rounded-full text-sm ${
              product.inStock 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {product.inStock ? '有库存' : '缺货'}
            </span>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <label htmlFor="quantity" className="text-sm font-medium">
                数量:
              </label>
              <select id="quantity" className="input w-20">
                <option>1</option>
                <option>2</option>
                <option>3</option>
                <option>4</option>
                <option>5</option>
              </select>
            </div>

            <button 
              className="btn btn-primary w-full"
              disabled={!product.inStock}
            >
              {product.inStock ? '加入购物车' : '缺货'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
