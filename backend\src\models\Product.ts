import mongoose, { Document, Schema } from 'mongoose';

export interface IProduct extends Document {
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: mongoose.Types.ObjectId;
  subcategory?: mongoose.Types.ObjectId;
  stock: number;
  sizes?: string[];
  colors?: {
    name: string;
    code: string;
  }[];
  tags?: string[];
  isNew: boolean;
  isFeatured: boolean;
  isActive: boolean;
  ratings: {
    userId: mongoose.Types.ObjectId;
    rating: number;
    comment?: string;
    createdAt: Date;
  }[];
  averageRating: number;
  numReviews: number;
  createdAt: Date;
  updatedAt: Date;
}

const ProductSchema = new Schema<IProduct>(
  {
    name: {
      type: String,
      required: [true, '商品名称不能为空'],
      trim: true,
      maxlength: [100, '商品名称不能超过100个字符'],
    },
    description: {
      type: String,
      required: [true, '商品描述不能为空'],
    },
    price: {
      type: Number,
      required: [true, '商品价格不能为空'],
      min: [0, '商品价格不能为负数'],
    },
    originalPrice: {
      type: Number,
      min: [0, '商品原价不能为负数'],
    },
    images: {
      type: [String],
      required: [true, '商品图片不能为空'],
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: 'Category',
      required: [true, '商品分类不能为空'],
    },
    subcategory: {
      type: Schema.Types.ObjectId,
      ref: 'Category',
    },
    stock: {
      type: Number,
      required: [true, '商品库存不能为空'],
      min: [0, '商品库存不能为负数'],
      default: 0,
    },
    sizes: {
      type: [String],
    },
    colors: [
      {
        name: { type: String, required: true },
        code: { type: String, required: true },
      },
    ],
    tags: {
      type: [String],
    },
    isNew: {
      type: Boolean,
      default: false,
    },
    isFeatured: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    ratings: [
      {
        userId: {
          type: Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        rating: {
          type: Number,
          required: true,
          min: 1,
          max: 5,
        },
        comment: {
          type: String,
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    averageRating: {
      type: Number,
      default: 0,
    },
    numReviews: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

// 添加评论时更新平均评分和评论数量
ProductSchema.pre('save', async function (next) {
  if (this.isModified('ratings')) {
    const totalRating = this.ratings.reduce((sum, item) => sum + item.rating, 0);
    this.averageRating = this.ratings.length > 0 ? totalRating / this.ratings.length : 0;
    this.numReviews = this.ratings.length;
  }
  next();
});

export default mongoose.model<IProduct>('Product', ProductSchema); 