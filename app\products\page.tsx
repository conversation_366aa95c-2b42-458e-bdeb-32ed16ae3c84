import ProductCard from '@/components/ProductCard';
import { getProducts } from '@/lib/data';

export default function ProductsPage() {
  const products = getProducts();

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">全部商品</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}
