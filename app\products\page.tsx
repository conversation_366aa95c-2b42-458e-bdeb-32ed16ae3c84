import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FALLBACK_PRODUCTS } from '@/utils/mockData';

// 定义类型
interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface Product {
  id: number;
  name: string;
  slug: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  images: ProductImage[];
  stock_status?: string;
}

export default function ProductsPage() {
  // 使用静态数据
  const products = FALLBACK_PRODUCTS;
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">全部产品</h1>
        <div className="flex items-center text-sm text-gray-500">
          <Link href="/" className="hover:text-primary">首页</Link>
          <span className="mx-2">/</span>
          <span>全部产品</span>
        </div>
      </div>
      
      {/* 产品过滤和排序 */}
      <div className="mb-8">
        <div className="bg-white p-4 rounded-lg shadow-sm flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <span className="text-gray-600">显示 {products.length} 个产品</span>
          </div>
          <div className="flex flex-wrap gap-4">
            <select className="form-control max-w-xs">
              <option value="default">默认排序</option>
              <option value="price-asc">价格从低到高</option>
              <option value="price-desc">价格从高到低</option>
              <option value="name-asc">名称 A-Z</option>
              <option value="name-desc">名称 Z-A</option>
              <option value="newest">最新产品</option>
            </select>
            <select className="form-control max-w-xs">
              <option value="all">所有状态</option>
              <option value="instock">有库存</option>
              <option value="outofstock">缺货</option>
              <option value="onsale">特价</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 产品列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {products.map((product) => (
          <div key={product.id} className="product-card group">
            <div className="product-image relative h-[300px] overflow-hidden rounded-t-lg">
              {product.on_sale && (
                <div className="product-badge absolute top-2 right-2 z-10">
                  <span className="badge badge-danger bg-red-500 text-white px-2 py-1 text-xs rounded">特价</span>
                </div>
              )}
              {product.stock_status === 'outofstock' && (
                <div className="product-badge absolute top-2 left-2 z-10">
                  <span className="badge badge-warning bg-yellow-500 text-white px-2 py-1 text-xs rounded">缺货</span>
                </div>
              )}
              <Link href={`/products/detail/${product.id}`}>
                {product.images && product.images.length > 0 ? (
                  <Image
                    src={product.images[0].src}
                    alt={product.name}
                    width={300}
                    height={300}
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    quality={75}
                    loading="lazy"
                    className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                    onError={(e) => {
                      // 图片加载错误时，设置为默认图片
                      const target = e.target as HTMLImageElement;
                      target.src = `/images/product${(product.id % 4) + 1}.jpg`;
                    }}
                  />
                ) : (
                  <Image
                    src={`/images/product${(product.id % 4) + 1}.jpg`}
                    alt={product.name}
                    width={300}
                    height={300}
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    quality={75}
                    loading="lazy"
                    className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                  />
                )}
              </Link>
            </div>
            <div className="product-info p-4 bg-white rounded-b-lg">
              <Link href={`/products/detail/${product.id}`} className="product-name text-lg font-medium hover:text-primary">
                {product.name}
              </Link>
              <div className="flex items-center mt-2">
                {product.on_sale ? (
                  <>
                    <span className="product-price text-primary font-bold">¥{product.sale_price}</span>
                    <span className="product-original-price text-gray-400 line-through ml-2 text-sm">¥{product.regular_price}</span>
                  </>
                ) : (
                  <span className="product-price text-primary font-bold">¥{product.price}</span>
                )}
              </div>
              <button 
                className={`mt-4 w-full btn ${
                  product.stock_status === 'outofstock' 
                    ? 'bg-gray-300 text-gray-600 cursor-not-allowed' 
                    : 'btn-primary bg-primary text-white transition-opacity opacity-80 group-hover:opacity-100'
                }`}
                disabled={product.stock_status === 'outofstock'}
              >
                {product.stock_status === 'outofstock' ? '缺货' : '加入购物车'}
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {/* 分页 */}
      <div className="flex justify-center">
        <div className="inline-flex rounded-md shadow-sm">
          <button className="px-4 py-2 border border-gray-300 rounded-l-md bg-white text-gray-500 hover:bg-gray-50">
            上一页
          </button>
          <button className="px-4 py-2 border-t border-b border-gray-300 bg-primary text-white">
            1
          </button>
          <button className="px-4 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
            2
          </button>
          <button className="px-4 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
            3
          </button>
          <button className="px-4 py-2 border border-gray-300 rounded-r-md bg-white text-gray-700 hover:bg-gray-50">
            下一页
          </button>
        </div>
      </div>
    </div>
  );
} 