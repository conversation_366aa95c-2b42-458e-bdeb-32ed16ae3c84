'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Link from 'next/link';
import { useState } from 'react';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function ClientLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* 左侧：品牌logo和导航 */}
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold text-primary flex-shrink-0">
                  时尚电商
                </Link>

                {/* 桌面端导航 */}
                <nav className="ml-8 hidden lg:block">
                  <ul className="flex space-x-8">
                    <li>
                      <Link href="/catalog/women" className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium">
                        女装
                      </Link>
                    </li>
                    <li>
                      <Link href="/catalog/men" className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium">
                        男装
                      </Link>
                    </li>
                    <li>
                      <Link href="/catalog/shoes" className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium">
                        鞋类
                      </Link>
                    </li>
                    <li>
                      <Link href="/catalog/accessories" className="text-gray-600 hover:text-primary transition-colors duration-200 font-medium">
                        配饰
                      </Link>
                    </li>
                  </ul>
                </nav>
              </div>
              {/* 右侧：搜索、用户菜单、购物车 */}
              <div className="flex items-center space-x-2 sm:space-x-4">
                {/* 搜索框 */}
                <div className="relative hidden sm:block">
                  <input
                    type="text"
                    placeholder="搜索产品..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-full w-48 lg:w-64 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                  />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>

                {/* 移动端搜索按钮 */}
                <button className="sm:hidden p-2 text-gray-600 hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
                {/* 用户菜单和购物车 */}
                <div className="flex items-center space-x-1 sm:space-x-3">
                  {isLoggedIn ? (
                    <>
                      <Link href="/account" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2">
                        <span className="hidden lg:inline font-medium">我的账户</span>
                        <span className="lg:hidden">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </span>
                      </Link>
                      {isAdmin && (
                        <Link href="/admin" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2">
                          <span className="hidden lg:inline font-medium">管理后台</span>
                          <span className="lg:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </span>
                        </Link>
                      )}
                      <button
                        onClick={() => setIsLoggedIn(false)}
                        className="text-gray-600 hover:text-primary transition-colors duration-200 p-2 hidden sm:block font-medium"
                      >
                        退出
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/login" className="text-gray-600 hover:text-primary transition-colors duration-200 p-2 font-medium hidden sm:block">
                        登录
                      </Link>
                      <Link href="/register" className="btn btn-primary btn-sm hidden sm:inline-flex">
                        注册
                      </Link>
                      <button
                        onClick={() => {
                          setIsLoggedIn(true);
                          setIsAdmin(true);
                        }}
                        className="text-xs text-gray-400 hover:text-primary transition-colors duration-200 p-1 hidden lg:block"
                      >
                        模拟登录
                      </button>
                    </>
                  )}

                  {/* 购物车 */}
                  <Link href="/cart" className="text-gray-600 hover:text-primary relative p-2 transition-colors duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-pink-600 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center font-medium">
                        {cartCount > 99 ? '99+' : cartCount}
                      </span>
                    )}
                  </Link>

                  {/* 移动端菜单按钮 */}
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="lg:hidden p-2 text-gray-600 hover:text-primary transition-colors duration-200"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </button>

                  {/* 开发用的购物车增加按钮 */}
                  <button
                    onClick={() => setCartCount(prev => prev + 1)}
                    className="text-xs text-gray-400 hover:text-primary transition-colors duration-200 p-1 hidden lg:block"
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 移动端菜单 */}
          {isMobileMenuOpen && (
            <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
              <div className="container mx-auto px-4 py-4">
                {/* 移动端搜索框 */}
                <div className="relative mb-4 sm:hidden">
                  <input
                    type="text"
                    placeholder="搜索产品..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>

                {/* 移动端导航菜单 */}
                <nav className="space-y-2">
                  <Link
                    href="/catalog/women"
                    className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    女装
                  </Link>
                  <Link
                    href="/catalog/men"
                    className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    男装
                  </Link>
                  <Link
                    href="/catalog/shoes"
                    className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    鞋类
                  </Link>
                  <Link
                    href="/catalog/accessories"
                    className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    配饰
                  </Link>
                </nav>

                {/* 移动端用户菜单 */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  {isLoggedIn ? (
                    <div className="space-y-2">
                      <Link
                        href="/account"
                        className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        我的账户
                      </Link>
                      {isAdmin && (
                        <Link
                          href="/admin"
                          className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          管理后台
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          setIsLoggedIn(false);
                          setIsMobileMenuOpen(false);
                        }}
                        className="block w-full text-left py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                      >
                        退出登录
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Link
                        href="/login"
                        className="block py-3 px-4 text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 font-medium"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        登录
                      </Link>
                      <Link
                        href="/register"
                        className="block py-3 px-4 bg-primary text-white hover:bg-primary-dark rounded-md transition-colors duration-200 font-medium text-center"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        注册
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </header>
        <main>{children}</main>
        <footer className="bg-gray-800 text-white py-12 mt-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="mb-8 sm:mb-0">
                <h3 className="text-lg font-semibold mb-4">关于我们</h3>
                <p className="text-gray-400 leading-relaxed">
                  时尚电商平台提供最新的时尚服装和配饰，致力于为顾客提供高品质的购物体验。
                </p>
              </div>
              <div className="mb-8 sm:mb-0">
                <h3 className="text-lg font-semibold mb-4">客户服务</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><Link href="/help" className="hover:text-white transition-colors duration-200">帮助中心</Link></li>
                  <li><Link href="/shipping" className="hover:text-white transition-colors duration-200">配送信息</Link></li>
                  <li><Link href="/returns" className="hover:text-white transition-colors duration-200">退换货政策</Link></li>
                  <li><Link href="/contact" className="hover:text-white transition-colors duration-200">联系我们</Link></li>
                </ul>
              </div>
              <div className="mb-8 sm:mb-0">
                <h3 className="text-lg font-semibold mb-4">账户</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><Link href="/login" className="hover:text-white transition-colors duration-200">登录</Link></li>
                  <li><Link href="/register" className="hover:text-white transition-colors duration-200">注册</Link></li>
                  <li><Link href="/account" className="hover:text-white transition-colors duration-200">我的账户</Link></li>
                  <li><Link href="/orders" className="hover:text-white transition-colors duration-200">订单查询</Link></li>
                </ul>
              </div>
              <div className="mb-8 sm:mb-0">
                <h3 className="text-lg font-semibold mb-4">关注我们</h3>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors duration-200" aria-label="Facebook">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z" />
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors duration-200" aria-label="Instagram">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors duration-200" aria-label="Twitter">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors duration-200" aria-label="微信">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.194-.242-2.391-2.92-4.441-6.525-4.441a8.770 8.770 0 0 0-2.072.247zm-5.46 7.876c-.663 0-1.201-.538-1.201-1.201s.538-1.201 1.201-1.201 1.201.538 1.201 1.201-.538 1.201-1.201 1.201zm5.01 0c-.663 0-1.201-.538-1.201-1.201s.538-1.201 1.201-1.201 1.201.538 1.201 1.201-.538 1.201-1.201 1.201z"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
            <div className="mt-12 pt-8 border-t border-gray-700 text-center text-gray-400">
              <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                <p>&copy; {new Date().getFullYear()} 时尚电商平台 版权所有</p>
                <div className="flex space-x-6 text-sm">
                  <Link href="/privacy" className="hover:text-white transition-colors duration-200">隐私政策</Link>
                  <Link href="/terms" className="hover:text-white transition-colors duration-200">服务条款</Link>
                  <Link href="/sitemap" className="hover:text-white transition-colors duration-200">网站地图</Link>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
} 