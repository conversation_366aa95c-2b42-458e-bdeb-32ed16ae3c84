import React from 'react';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/auth';
import { getProducts } from '@/utils/wordpress';
import { FaEdit, FaTrash, FaEye, FaPlus } from 'react-icons/fa';
import { IMAGES } from '@/utils/imageConstants';

// 定义产品类型
interface Category {
  id: number;
  name: string;
  slug: string;
}

interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

interface Product {
  id: number;
  name: string;
  slug: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  stock_quantity: number;
  stock_status: string;
  categories: Category[];
  images: ProductImage[];
}

export default async function AdminProductsPage() {
  // 检查用户是否已登录
  const session = await getServerSession(authOptions);
  
  // 如果未登录，重定向到登录页
  if (!session) {
    redirect('/login?callbackUrl=/admin/products');
  }
  
  // 获取产品列表
  const { products } = await getProducts();
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">产品管理</h1>
        <Link href="/admin/products/new" className="btn btn-primary flex items-center">
          <FaPlus className="mr-2" />
          添加产品
        </Link>
      </div>
      
      {/* 筛选和搜索 */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索产品..."
              className="form-input"
            />
          </div>
          <div className="flex gap-4">
            <select className="form-select">
              <option value="">所有分类</option>
              <option value="men">男装</option>
              <option value="women">女装</option>
              <option value="shoes">鞋类</option>
              <option value="accessories">配饰</option>
            </select>
            <select className="form-select">
              <option value="">所有状态</option>
              <option value="instock">有库存</option>
              <option value="outofstock">缺货</option>
              <option value="onbackorder">预订</option>
            </select>
            <button className="btn btn-primary">筛选</button>
          </div>
        </div>
      </div>
      
      {/* 产品列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-gray-700">图片</th>
                <th className="px-4 py-3 text-left text-gray-700">产品名称</th>
                <th className="px-4 py-3 text-left text-gray-700">分类</th>
                <th className="px-4 py-3 text-left text-gray-700">价格</th>
                <th className="px-4 py-3 text-left text-gray-700">库存</th>
                <th className="px-4 py-3 text-left text-gray-700">状态</th>
                <th className="px-4 py-3 text-left text-gray-700">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {products.map((product: Product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div className="w-12 h-12 relative">
                      {product.images && product.images.length > 0 ? (
                        <img
                          src={product.images[0].src || IMAGES.product(product.id)}
                          alt={product.name}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <img
                          src={IMAGES.product(product.id)}
                          alt={product.name}
                          className="w-full h-full object-cover rounded"
                        />
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-3 font-medium">{product.name}</td>
                  <td className="px-4 py-3">
                    {product.categories && product.categories.map((cat: Category) => cat.name).join(', ')}
                  </td>
                  <td className="px-4 py-3">
                    {product.on_sale ? (
                      <div>
                        <span className="line-through text-gray-400">¥{product.regular_price}</span>
                        <span className="ml-2 text-red-500">¥{product.sale_price}</span>
                      </div>
                    ) : (
                      <span>¥{product.price}</span>
                    )}
                  </td>
                  <td className="px-4 py-3">{product.stock_quantity || 0}</td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      product.stock_status === 'instock' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.stock_status === 'instock' ? '有库存' : '缺货'}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <Link href={`/products/detail/${product.id}`} className="text-blue-500 hover:text-blue-700">
                        <FaEye />
                      </Link>
                      <Link href={`/admin/products/edit/${product.id}`} className="text-green-500 hover:text-green-700">
                        <FaEdit />
                      </Link>
                      <button className="text-red-500 hover:text-red-700">
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* 分页 */}
        <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            显示 <span className="font-medium">1</span> 到 <span className="font-medium">{products.length}</span> 共 <span className="font-medium">{products.length}</span> 条
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 border border-gray-300 rounded text-sm" disabled>上一页</button>
            <button className="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
            <button className="px-3 py-1 border border-gray-300 rounded text-sm" disabled>下一页</button>
          </div>
        </div>
      </div>
    </div>
  );
} 