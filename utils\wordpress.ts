// 本地数据服务，替代WordPress API
import { FALLBACK_PRODUCTS, FALLBACK_CATEGORIES } from './mockData';

// 定义类型
export interface ProductImage {
  id: number;
  src: string;
  name: string;
  alt: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  count?: number;
  description?: string;
  image?: {
    id: number;
    src: string;
    alt: string;
  };
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
}

export interface Attribute {
  id: number;
  name: string;
  position: number;
  visible: boolean;
  variation: boolean;
  options: string[];
}

export interface Product {
  id: number;
  name: string;
  slug: string;
  permalink?: string;
  date_created?: string;
  type?: string;
  status?: string;
  featured?: boolean;
  catalog_visibility?: string;
  description?: string;
  short_description?: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  purchasable?: boolean;
  total_sales?: number;
  virtual?: boolean;
  downloadable?: boolean;
  categories: Category[];
  tags?: Tag[];
  images: ProductImage[];
  attributes?: Attribute[];
  stock_quantity?: number;
  stock_status?: string;
}

// 内存缓存
const CACHE_TTL = 60 * 60 * 1000; // 1小时缓存
type CacheItem = {
  data: any;
  timestamp: number;
};
const cache: Record<string, CacheItem> = {};

// 缓存工具函数
function getFromCache<T>(key: string): T | null {
  const item = cache[key];
  if (!item) return null;
  
  const now = Date.now();
  if (now - item.timestamp > CACHE_TTL) {
    delete cache[key];
    return null;
  }
  
  return item.data as T;
}

function setCache(key: string, data: any): void {
  cache[key] = {
    data,
    timestamp: Date.now()
  };
}

// 获取产品列表
export async function getProducts(page = 1, perPage = 20) {
  const cacheKey = `products_${page}_${perPage}`;
  const cachedData = getFromCache(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  try {
    // 使用本地模拟数据
    const result = {
      products: FALLBACK_PRODUCTS,
      total: FALLBACK_PRODUCTS.length,
      totalPages: Math.ceil(FALLBACK_PRODUCTS.length / perPage)
    };
    
    setCache(cacheKey, result);
    return result;
  } catch (error) {
    console.error('获取产品列表失败:', error);
    return {
      products: FALLBACK_PRODUCTS,
      total: FALLBACK_PRODUCTS.length,
      totalPages: Math.ceil(FALLBACK_PRODUCTS.length / perPage)
    };
  }
}

// 获取单个产品
export async function getProduct(id: string): Promise<{ product: Product | null }> {
  const cacheKey = `product_${id}`;
  const cachedData = getFromCache<{ product: Product | null }>(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  try {
    const product = FALLBACK_PRODUCTS.find((p: Product) => p.id.toString() === id);
    const result = { product: product || null };
    
    setCache(cacheKey, result);
    return result;
  } catch (error) {
    console.error(`获取产品 ${id} 失败:`, error);
    return { product: null };
  }
}

// 获取分类列表
export async function getCategories() {
  const cacheKey = 'categories';
  const cachedData = getFromCache(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  try {
    setCache(cacheKey, FALLBACK_CATEGORIES);
    return FALLBACK_CATEGORIES;
  } catch (error) {
    console.error('获取分类列表失败:', error);
    return FALLBACK_CATEGORIES;
  }
}

// 按分类获取产品
export async function getProductsByCategory(categoryId: string | number, page = 1, perPage = 20) {
  const cacheKey = `products_category_${categoryId}_${page}_${perPage}`;
  const cachedData = getFromCache(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  try {
    const filteredProducts = FALLBACK_PRODUCTS.filter((product: Product) => 
      product.categories.some((category: Category) => category.id.toString() === categoryId.toString())
    );
    
    const result = {
      products: filteredProducts,
      total: filteredProducts.length,
      totalPages: Math.ceil(filteredProducts.length / perPage)
    };
    
    setCache(cacheKey, result);
    return result;
  } catch (error) {
    console.error(`获取分类 ${categoryId} 的产品失败:`, error);
    return {
      products: [],
      total: 0,
      totalPages: 0
    };
  }
}

// 其他需要的API函数可以根据需要添加
export async function createProduct(productData: Partial<Product>) {
  console.log('创建产品:', productData);
  return { success: true, product: { ...productData, id: Date.now() } };
}

export async function updateProduct(id: string | number, productData: Partial<Product>) {
  console.log('更新产品:', id, productData);
  
  // 清除相关缓存
  Object.keys(cache).forEach(key => {
    if (key.startsWith('product_') || key.startsWith('products_')) {
      delete cache[key];
    }
  });
  
  return { success: true, product: { ...productData, id } };
}

export async function deleteProduct(id: string | number) {
  console.log('删除产品:', id);
  
  // 清除相关缓存
  Object.keys(cache).forEach(key => {
    if (key.startsWith('product_') || key.startsWith('products_')) {
      delete cache[key];
    }
  });
  
  return { success: true };
} 