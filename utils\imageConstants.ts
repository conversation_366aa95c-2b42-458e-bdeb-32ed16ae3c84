// 图片常量和工具函数

// 基础图片服务配置
export const IMAGE_CONFIG = {
  // 使用 Picsum Photos 作为占位图片服务
  baseUrl: 'https://picsum.photos',
  // 备用图片服务
  fallbackUrl: 'https://via.placeholder.com',
  // 图片质量设置
  quality: {
    low: 50,
    medium: 75,
    high: 90,
  },
  // 常用尺寸
  sizes: {
    thumbnail: { width: 150, height: 150 },
    small: { width: 300, height: 300 },
    medium: { width: 600, height: 600 },
    large: { width: 1200, height: 800 },
    hero: { width: 1920, height: 1080 },
  },
};

// 生成图片URL的工具函数
export const generateImageUrl = (
  type: 'product' | 'category' | 'hero' | 'avatar',
  id?: number | string,
  width: number = 400,
  height: number = 400,
  blur: boolean = false
): string => {
  const seed = id ? `${type}-${id}` : `${type}-default`;
  const blurParam = blur ? '?blur=2' : '';
  return `${IMAGE_CONFIG.baseUrl}/seed/${seed}/${width}/${height}${blurParam}`;
};

// 预定义的图片URL
export const IMAGES = {
  // 首页横幅图片
  hero: {
    main: generateImageUrl('hero', 'main', 1920, 800),
    fashion: generateImageUrl('hero', 'fashion', 1920, 600),
    sale: generateImageUrl('hero', 'sale', 1920, 600),
  },
  
  // 分类图片
  categories: {
    women: generateImageUrl('category', 'women', 600, 400),
    men: generateImageUrl('category', 'men', 600, 400),
    shoes: generateImageUrl('category', 'shoes', 600, 400),
    accessories: generateImageUrl('category', 'accessories', 600, 400),
    bags: generateImageUrl('category', 'bags', 600, 400),
    jewelry: generateImageUrl('category', 'jewelry', 600, 400),
  },
  
  // 分类横幅图片
  categoryBanners: {
    women: generateImageUrl('category', 'women-banner', 1200, 400),
    men: generateImageUrl('category', 'men-banner', 1200, 400),
    shoes: generateImageUrl('category', 'shoes-banner', 1200, 400),
    accessories: generateImageUrl('category', 'accessories-banner', 1200, 400),
    bags: generateImageUrl('category', 'bags-banner', 1200, 400),
    jewelry: generateImageUrl('category', 'jewelry-banner', 1200, 400),
  },
  
  // 产品图片生成函数
  product: (id: number, variant: number = 1) => 
    generateImageUrl('product', `${id}-${variant}`, 600, 600),
  
  // 产品缩略图
  productThumbnail: (id: number, variant: number = 1) => 
    generateImageUrl('product', `${id}-${variant}`, 300, 300),
  
  // 用户头像
  avatar: (id: number | string = 'default') => 
    generateImageUrl('avatar', id, 150, 150),
  
  // 默认占位图片
  placeholder: {
    product: generateImageUrl('product', 'placeholder', 400, 400),
    category: generateImageUrl('category', 'placeholder', 400, 300),
    avatar: generateImageUrl('avatar', 'placeholder', 150, 150),
    hero: generateImageUrl('hero', 'placeholder', 1200, 600),
  },
};

// 图片尺寸配置
export const IMAGE_SIZES = {
  // 产品图片
  product: {
    thumbnail: '150px',
    card: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw',
    detail: '(max-width: 1024px) 100vw, 50vw',
    gallery: '20vw',
  },
  
  // 分类图片
  category: {
    card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw',
    banner: '100vw',
  },
  
  // 横幅图片
  hero: '100vw',
  
  // 头像
  avatar: '150px',
};

// 图片加载优先级配置
export const IMAGE_PRIORITY = {
  hero: true,
  categoryBanner: true,
  firstProduct: true,
  others: false,
};

// 生成产品图片数组的工具函数
export const generateProductImages = (productId: number, count: number = 4) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    src: IMAGES.product(productId, index + 1),
    name: `产品图片 ${index + 1}`,
    alt: `产品 ${productId} - 图片 ${index + 1}`,
  }));
};

// 获取分类图片的工具函数
export const getCategoryImage = (categorySlug: string): string => {
  const categoryImages = IMAGES.categories as Record<string, string>;
  return categoryImages[categorySlug] || IMAGES.placeholder.category;
};

// 获取分类横幅图片的工具函数
export const getCategoryBanner = (categorySlug: string): string => {
  const bannerImages = IMAGES.categoryBanners as Record<string, string>;
  return bannerImages[categorySlug] || IMAGES.placeholder.hero;
};

// 图片预加载函数 - 优化版本
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查图片是否已经在缓存中
    if (typeof window !== 'undefined') {
      const img = new Image();

      // 设置超时
      const timeout = setTimeout(() => {
        reject(new Error('Image preload timeout'));
      }, 10000); // 10秒超时

      img.onload = () => {
        clearTimeout(timeout);
        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('Image preload failed'));
      };

      img.src = src;
    } else {
      resolve(); // 服务端直接resolve
    }
  });
};

// 批量预加载图片 - 优化版本
export const preloadImages = async (urls: string[]): Promise<void> => {
  if (typeof window === 'undefined') return;

  try {
    // 限制并发数量，避免过多请求
    const batchSize = 3;
    const batches = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }

    // 逐批预加载
    for (const batch of batches) {
      await Promise.allSettled(batch.map(preloadImage));
      // 在批次之间添加小延迟，避免阻塞主线程
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  } catch (error) {
    console.warn('Some images failed to preload:', error);
  }
};

// 智能预加载 - 根据网络状况调整
export const smartPreloadImages = async (urls: string[]): Promise<void> => {
  if (typeof window === 'undefined') return;

  // 检查网络状况
  const connection = (navigator as any).connection;
  if (connection) {
    // 如果是慢速网络，只预加载关键图片
    if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
      return preloadImages(urls.slice(0, 2)); // 只预加载前2张
    }

    // 如果是节省流量模式，跳过预加载
    if (connection.saveData) {
      return;
    }
  }

  // 正常预加载
  return preloadImages(urls);
};
