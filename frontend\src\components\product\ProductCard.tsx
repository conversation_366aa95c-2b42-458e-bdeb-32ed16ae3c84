import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaHeart, FaShoppingCart } from 'react-icons/fa';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isNew?: boolean;
  isSale?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  name,
  price,
  originalPrice,
  image,
  category,
  isNew = false,
  isSale = false,
}) => {
  const discount = originalPrice ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0;

  return (
    <div className="card group relative">
      {/* 商品图片 */}
      <div className="relative overflow-hidden aspect-[3/4]">
        <Link href={`/products/${id}`}>
          <Image
            src={image}
            alt={name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
        </Link>

        {/* 标签 */}
        <div className="absolute top-2 left-2 flex flex-col gap-2">
          {isNew && (
            <span className="bg-primary text-white px-2 py-1 text-xs font-medium rounded">
              新品
            </span>
          )}
          {isSale && (
            <span className="bg-secondary text-white px-2 py-1 text-xs font-medium rounded">
              特价 {discount}% OFF
            </span>
          )}
        </div>

        {/* 快捷操作按钮 */}
        <div className="absolute right-2 top-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors"
            title="添加到收藏"
          >
            <FaHeart className="text-gray-600 hover:text-secondary" size={18} />
          </button>
          <button
            className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors"
            title="添加到购物车"
          >
            <FaShoppingCart className="text-gray-600 hover:text-primary" size={18} />
          </button>
        </div>
      </div>

      {/* 商品信息 */}
      <div className="p-4">
        <div className="text-sm text-gray-500 mb-1">{category}</div>
        <Link href={`/products/${id}`} className="block">
          <h3 className="font-medium text-lg mb-2 hover:text-primary transition-colors">
            {name}
          </h3>
        </Link>
        <div className="flex items-center">
          <span className="font-bold text-lg">¥{price.toFixed(2)}</span>
          {originalPrice && (
            <span className="text-gray-400 line-through ml-2">¥{originalPrice.toFixed(2)}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard; 