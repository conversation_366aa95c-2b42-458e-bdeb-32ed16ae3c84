import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const LoginForm: React.FC = () => {
  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email('邮箱格式不正确')
        .required('邮箱不能为空'),
      password: Yup.string()
        .required('密码不能为空'),
    }),
    onSubmit: (values) => {
      console.log('登录表单提交:', values);
      // 这里将调用API进行登录
    },
  });

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">用户登录</h2>
      <form onSubmit={formik.handleSubmit}>
        <div className="form-control">
          <label htmlFor="email">邮箱</label>
          <input
            id="email"
            name="email"
            type="email"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.email}
          />
          {formik.touched.email && formik.errors.email ? (
            <div className="text-error text-sm mt-1">{formik.errors.email}</div>
          ) : null}
        </div>

        <div className="form-control">
          <label htmlFor="password">密码</label>
          <input
            id="password"
            name="password"
            type="password"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.password}
          />
          {formik.touched.password && formik.errors.password ? (
            <div className="text-error text-sm mt-1">{formik.errors.password}</div>
          ) : null}
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center">
            <input
              id="rememberMe"
              name="rememberMe"
              type="checkbox"
              onChange={formik.handleChange}
              checked={formik.values.rememberMe}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
              记住我
            </label>
          </div>
          <div className="text-sm">
            <a href="/forgot-password" className="text-primary hover:text-primary-dark">
              忘记密码?
            </a>
          </div>
        </div>

        <div className="mt-6">
          <button
            type="submit"
            className="w-full btn btn-primary"
            disabled={formik.isSubmitting}
          >
            {formik.isSubmitting ? '登录中...' : '登录'}
          </button>
        </div>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            还没有账号?{' '}
            <a href="/register" className="text-primary hover:text-primary-dark">
              立即注册
            </a>
          </p>
        </div>
      </form>
    </div>
  );
};

export default LoginForm; 