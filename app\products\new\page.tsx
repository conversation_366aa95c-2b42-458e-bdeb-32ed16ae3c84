import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { IMAGES } from '@/utils/imageConstants';
import OptimizedImage from '@/components/ui/OptimizedImage';

// 模拟产品数据
const allProducts = [
  {
    id: '1',
    name: '夏季轻薄连衣裙',
    price: 299,
    originalPrice: 399,
    image: IMAGES.product(1),
    category: '女装',
    isNew: true,
    isSale: true,
  },
  {
    id: '3',
    name: '时尚牛仔裤',
    price: 199,
    image: IMAGES.product(3),
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '5',
    name: '真丝衬衫',
    price: 499,
    originalPrice: 599,
    image: IMAGES.product(5),
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '7',
    name: '针织开衫',
    price: 359,
    originalPrice: 459,
    image: IMAGES.product(7),
    category: '女装',
    isNew: true,
    isSale: true,
  },
];

export default function NewProductsPage() {
  // 筛选新品
  const newProducts = allProducts.filter(product => product.isNew);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">新品上市</h1>
      
      {/* 筛选区域 */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            显示 <span className="font-medium">{newProducts.length}</span> 件新品
          </div>
          
          <div className="flex items-center">
            <label htmlFor="sort" className="mr-2 text-gray-600">排序:</label>
            <select
              id="sort"
              className="border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-primary"
            >
              <option value="newest">最新上架</option>
              <option value="price-low">价格从低到高</option>
              <option value="price-high">价格从高到低</option>
              <option value="popular">热门商品</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 商品列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {newProducts.length > 0 ? (
          newProducts.map((product) => (
            <div key={product.id} className="card group relative">
              <div className="relative overflow-hidden aspect-[3/4]">
                <Link href={`/products/detail/${product.id}`}>
                  <OptimizedImage
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    fallbackType="product"
                    fallbackId={Number(product.id)}
                  />
                </Link>
                <div className="absolute top-2 left-2 flex flex-col gap-2">
                  {product.isNew && (
                    <span className="bg-primary text-white px-2 py-1 text-xs font-medium rounded">
                      新品
                    </span>
                  )}
                  {product.isSale && (
                    <span className="bg-secondary text-white px-2 py-1 text-xs font-medium rounded">
                      特价
                    </span>
                  )}
                </div>
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 mb-1">{product.category}</div>
                <Link href={`/products/detail/${product.id}`} className="block">
                  <h3 className="font-medium text-lg mb-2 hover:text-primary transition-colors">
                    {product.name}
                  </h3>
                </Link>
                <div className="flex items-center">
                  <span className="font-bold text-lg">¥{product.price.toFixed(2)}</span>
                  {product.originalPrice && (
                    <span className="text-gray-400 line-through ml-2">
                      ¥{product.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-4 text-center py-12">
            <p className="text-xl text-gray-500">暂无新品</p>
          </div>
        )}
      </div>
    </div>
  );
} 