import React from 'react';

export default function PrivacyPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">隐私政策</h1>
        <p className="text-gray-600 text-center mb-12">最后更新时间：2023年12月1日</p>
        
        <div className="prose max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">1. 信息收集</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              我们收集您在使用我们服务时提供的信息，包括但不限于：
            </p>
            <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
              <li>注册账户时提供的个人信息（姓名、邮箱、电话号码等）</li>
              <li>购物时提供的配送地址和支付信息</li>
              <li>浏览网站时的行为数据和偏好设置</li>
              <li>与客服沟通时的记录</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">2. 信息使用</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              我们使用收集的信息用于以下目的：
            </p>
            <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
              <li>提供和改进我们的产品和服务</li>
              <li>处理您的订单和支付</li>
              <li>发送订单确认和物流更新</li>
              <li>提供客户支持</li>
              <li>发送营销信息（需要您的同意）</li>
              <li>防止欺诈和确保安全</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">3. 信息共享</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              我们不会出售、交易或转让您的个人信息给第三方，除非：
            </p>
            <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
              <li>获得您的明确同意</li>
              <li>为完成交易需要（如物流配送）</li>
              <li>法律要求或政府部门要求</li>
              <li>保护我们的权利、财产或安全</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">4. 数据安全</h2>
            <p className="text-gray-600 leading-relaxed">
              我们采用行业标准的安全措施来保护您的个人信息，包括加密传输、安全存储和访问控制。
              我们定期审查和更新安全措施，以确保您的信息得到最佳保护。
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">5. Cookie使用</h2>
            <p className="text-gray-600 leading-relaxed">
              我们使用Cookie和类似技术来改善您的浏览体验、分析网站流量和个性化内容。
              您可以通过浏览器设置管理Cookie偏好。
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">6. 您的权利</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              您对自己的个人信息享有以下权利：
            </p>
            <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
              <li>访问和查看您的个人信息</li>
              <li>更正不准确的信息</li>
              <li>删除您的个人信息</li>
              <li>限制信息处理</li>
              <li>数据可携带性</li>
              <li>反对某些处理活动</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">7. 儿童隐私</h2>
            <p className="text-gray-600 leading-relaxed">
              我们的服务不面向13岁以下的儿童。我们不会故意收集13岁以下儿童的个人信息。
              如果我们发现收集了此类信息，将立即删除。
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">8. 政策更新</h2>
            <p className="text-gray-600 leading-relaxed">
              我们可能会不时更新本隐私政策。重大变更将通过网站通知或邮件形式告知您。
              继续使用我们的服务即表示您接受更新后的政策。
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">9. 联系我们</h2>
            <p className="text-gray-600 leading-relaxed">
              如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：
            </p>
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600"><strong>邮箱：</strong> <EMAIL></p>
              <p className="text-gray-600"><strong>电话：</strong> 400-888-8888</p>
              <p className="text-gray-600"><strong>地址：</strong> 北京市朝阳区时尚大厦18层</p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
