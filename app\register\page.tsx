'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // 验证表单
    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    if (!formData.agreeTerms) {
      setError('请同意用户协议和隐私政策');
      return;
    }

    setLoading(true);

    try {
      // 模拟注册请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟成功注册
      console.log('注册成功:', formData);
      router.push('/login');
    } catch (err) {
      setError('注册失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="form-card">
        <div className="form-card-header">
          <h1 className="form-card-title">创建账户</h1>
          <p className="form-card-subtitle">加入我们，开始您的时尚之旅</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="form-group">
          <div className="form-control">
            <label htmlFor="name">姓名</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="请输入您的姓名"
              required
            />
          </div>

          <div className="form-control">
            <label htmlFor="email">电子邮箱</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="请输入您的电子邮箱"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-control">
              <label htmlFor="password">密码</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="请输入密码"
                required
                minLength={6}
              />
              <div className="help-text">密码至少包含6个字符</div>
            </div>

            <div className="form-control">
              <label htmlFor="confirmPassword">确认密码</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="请再次输入密码"
                required
              />
            </div>
          </div>

          <div className="flex items-start">
            <input
              type="checkbox"
              id="agreeTerms"
              name="agreeTerms"
              checked={formData.agreeTerms}
              onChange={handleChange}
              className="form-control mt-1"
              required
            />
            <label htmlFor="agreeTerms" className="ml-3 text-sm text-gray-700 leading-relaxed">
              我已阅读并同意
              <Link href="/terms" className="text-primary hover:text-primary-dark font-semibold transition-colors duration-200">用户协议</Link>
              {' '}和{' '}
              <Link href="/privacy" className="text-primary hover:text-primary-dark font-semibold transition-colors duration-200">隐私政策</Link>
            </label>
          </div>

          <button
            type="submit"
            className={`btn btn-primary w-full btn-lg ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                注册中...
              </div>
            ) : (
              '创建账户'
            )}
          </button>
        </form>

        <div className="form-card-footer">
          <p>
            已有账户? {' '}
            <Link href="/login" className="text-primary hover:text-primary-dark font-semibold transition-colors duration-200">
              立即登录
            </Link>
          </p>
        </div>
      </div>
      </div>
    </div>
  );
} 