import React from 'react';
import Link from 'next/link';
import { 
  FaHome, 
  FaBox, 
  FaTags, 
  FaShoppingCart, 
  FaUsers, 
  FaCog, 
  FaChartBar,
  FaSignOutAlt,
  FaBars
} from 'react-icons/fa';

// 侧边栏菜单项组件
function SidebarMenuItem({ 
  icon, 
  label, 
  href, 
  isActive = false 
}: { 
  icon: React.ReactNode; 
  label: string; 
  href: string; 
  isActive?: boolean;
}) {
  return (
    <Link 
      href={href} 
      className={`flex items-center px-4 py-3 text-sm ${
        isActive 
          ? 'bg-primary text-white' 
          : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      <span className="mr-3">{icon}</span>
      <span>{label}</span>
    </Link>
  );
}

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  // 当前路径，实际使用时应该从路由中获取
  const currentPath = '/admin';
  
  // 侧边栏菜单项
  const menuItems = [
    { icon: <FaHome />, label: '控制台', href: '/admin' },
    { icon: <FaBox />, label: '产品管理', href: '/admin/products' },
    { icon: <FaTags />, label: '分类管理', href: '/admin/categories' },
    { icon: <FaShoppingCart />, label: '订单管理', href: '/admin/orders' },
    { icon: <FaUsers />, label: '用户管理', href: '/admin/users' },
    { icon: <FaChartBar />, label: '统计分析', href: '/admin/analytics' },
    { icon: <FaCog />, label: '系统设置', href: '/admin/settings' },
  ];

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* 侧边栏 */}
      <aside className="w-64 bg-white shadow-md hidden md:block">
        {/* 品牌标志 */}
        <div className="p-4 border-b border-gray-200">
          <Link href="/admin" className="flex items-center">
            <span className="text-xl font-bold text-primary">时尚服饰</span>
            <span className="ml-2 bg-primary text-white text-xs px-2 py-1 rounded">管理后台</span>
          </Link>
        </div>
        
        {/* 导航菜单 */}
        <nav className="py-4">
          {menuItems.map((item, index) => (
            <SidebarMenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={currentPath === item.href}
            />
          ))}
          
          <div className="px-4 py-6">
            <div className="border-t border-gray-200 pt-4">
              <Link href="/api/auth/signout" className="flex items-center text-red-500 hover:bg-red-50 px-4 py-3 text-sm">
                <FaSignOutAlt className="mr-3" />
                <span>退出登录</span>
              </Link>
            </div>
          </div>
        </nav>
      </aside>
      
      {/* 主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部导航 */}
        <header className="bg-white shadow-sm z-10">
          <div className="flex justify-between items-center px-4 py-3">
            {/* 移动端菜单按钮 */}
            <button className="md:hidden text-gray-600">
              <FaBars />
            </button>
            
            {/* 页面标题 */}
            <h1 className="text-lg font-medium md:hidden">管理后台</h1>
            
            {/* 用户信息 */}
            <div className="flex items-center">
              <div className="mr-2 text-right hidden sm:block">
                <div className="text-sm font-medium">管理员</div>
                <div className="text-xs text-gray-500"><EMAIL></div>
              </div>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </header>
        
        {/* 页面内容 */}
        <main className="flex-1 overflow-y-auto bg-gray-100">
          {children}
        </main>
      </div>
    </div>
  );
} 