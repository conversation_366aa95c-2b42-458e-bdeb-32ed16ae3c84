'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  FaHome,
  FaBox,
  FaTags,
  FaShoppingCart,
  FaUsers,
  FaCog,
  FaChartBar,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaBell,
  FaUser
} from 'react-icons/fa';

// 侧边栏菜单项组件
function SidebarMenuItem({
  icon,
  label,
  href,
  isActive = false,
  onClick
}: {
  icon: React.ReactNode;
  label: string;
  href: string;
  isActive?: boolean;
  onClick?: () => void;
}) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={`flex items-center px-4 py-3 text-sm transition-all duration-200 ${
        isActive
          ? 'bg-primary text-white border-r-4 border-primary-light'
          : 'text-gray-600 hover:bg-gray-100 hover:text-primary'
      }`}
    >
      <span className="mr-3 text-lg">{icon}</span>
      <span className="font-medium">{label}</span>
    </Link>
  );
}

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // 侧边栏菜单项
  const menuItems = [
    { icon: <FaHome />, label: '控制台', href: '/admin' },
    { icon: <FaBox />, label: '产品管理', href: '/admin/products' },
    { icon: <FaTags />, label: '分类管理', href: '/admin/categories' },
    { icon: <FaShoppingCart />, label: '订单管理', href: '/admin/orders' },
    { icon: <FaUsers />, label: '用户管理', href: '/admin/users' },
    { icon: <FaChartBar />, label: '统计分析', href: '/admin/analytics' },
    { icon: <FaCog />, label: '系统设置', href: '/admin/settings' },
  ];

  const closeMobileSidebar = () => setIsMobileSidebarOpen(false);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 移动端遮罩层 */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={closeMobileSidebar}
        />
      )}

      {/* 桌面端侧边栏 */}
      <aside className="w-64 bg-white shadow-lg hidden md:block border-r border-gray-200">
        {/* 品牌标志 */}
        <div className="p-6 border-b border-gray-200">
          <Link href="/admin" className="flex items-center">
            <span className="text-xl font-bold text-primary">时尚服饰</span>
            <span className="ml-2 bg-primary text-white text-xs px-2 py-1 rounded-full">管理后台</span>
          </Link>
        </div>

        {/* 导航菜单 */}
        <nav className="py-4">
          {menuItems.map((item, index) => (
            <SidebarMenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={pathname === item.href}
            />
          ))}

          <div className="px-4 py-6">
            <div className="border-t border-gray-200 pt-4">
              <Link href="/" className="flex items-center text-gray-600 hover:bg-gray-100 hover:text-primary px-4 py-3 text-sm transition-colors duration-200">
                <FaHome className="mr-3" />
                <span>返回前台</span>
              </Link>
              <Link href="/api/auth/signout" className="flex items-center text-red-500 hover:bg-red-50 px-4 py-3 text-sm transition-colors duration-200">
                <FaSignOutAlt className="mr-3" />
                <span>退出登录</span>
              </Link>
            </div>
          </div>
        </nav>
      </aside>

      {/* 移动端侧边栏 */}
      <aside className={`fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out md:hidden ${
        isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* 品牌标志和关闭按钮 */}
        <div className="p-6 border-b border-gray-200 flex items-center justify-between">
          <Link href="/admin" className="flex items-center" onClick={closeMobileSidebar}>
            <span className="text-xl font-bold text-primary">时尚服饰</span>
            <span className="ml-2 bg-primary text-white text-xs px-2 py-1 rounded-full">管理后台</span>
          </Link>
          <button
            onClick={closeMobileSidebar}
            className="p-2 text-gray-600 hover:text-primary"
          >
            <FaTimes />
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="py-4">
          {menuItems.map((item, index) => (
            <SidebarMenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={pathname === item.href}
              onClick={closeMobileSidebar}
            />
          ))}

          <div className="px-4 py-6">
            <div className="border-t border-gray-200 pt-4">
              <Link href="/" className="flex items-center text-gray-600 hover:bg-gray-100 hover:text-primary px-4 py-3 text-sm transition-colors duration-200" onClick={closeMobileSidebar}>
                <FaHome className="mr-3" />
                <span>返回前台</span>
              </Link>
              <Link href="/api/auth/signout" className="flex items-center text-red-500 hover:bg-red-50 px-4 py-3 text-sm transition-colors duration-200" onClick={closeMobileSidebar}>
                <FaSignOutAlt className="mr-3" />
                <span>退出登录</span>
              </Link>
            </div>
          </div>
        </nav>
      </aside>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 顶部导航 */}
        <header className="bg-white shadow-sm z-10 border-b border-gray-200">
          <div className="flex justify-between items-center px-4 sm:px-6 lg:px-8 py-4">
            {/* 左侧：移动端菜单按钮 */}
            <div className="flex items-center">
              <button
                onClick={() => setIsMobileSidebarOpen(true)}
                className="md:hidden p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-md transition-colors duration-200"
              >
                <FaBars />
              </button>
              <h1 className="text-xl font-semibold text-gray-900 md:hidden ml-2">管理后台</h1>
            </div>

            {/* 右侧：通知和用户信息 */}
            <div className="flex items-center space-x-4">
              {/* 通知按钮 */}
              <button className="relative p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-md transition-colors duration-200">
                <FaBell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
                  3
                </span>
              </button>

              {/* 用户信息 */}
              <div className="flex items-center space-x-3">
                <div className="hidden sm:block text-right">
                  <div className="text-sm font-medium text-gray-900">管理员</div>
                  <div className="text-xs text-gray-500"><EMAIL></div>
                </div>
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <FaUser className="text-white text-sm" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          <div className="min-h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}