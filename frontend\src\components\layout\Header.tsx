import React from 'react';
import Link from 'next/link';
import { FaSearch, FaShoppingCart, FaUser } from 'react-icons/fa';

const Header: React.FC = () => {
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto py-4">
        <div className="flex items-center justify-between">
          {/* 品牌 Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-2xl font-bold text-primary">
              时尚服饰
            </Link>
          </div>

          {/* 导航菜单 */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-primary">
              首页
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-primary">
              全部商品
            </Link>
            <Link href="/products/men" className="text-gray-700 hover:text-primary">
              男装
            </Link>
            <Link href="/products/women" className="text-gray-700 hover:text-primary">
              女装
            </Link>
            <Link href="/products/accessories" className="text-gray-700 hover:text-primary">
              配饰
            </Link>
          </nav>

          {/* 搜索、购物车和用户菜单 */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索商品..."
                className="pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:border-primary"
              />
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            <Link href="/cart" className="text-gray-700 hover:text-primary relative">
              <FaShoppingCart size={24} />
              <span className="absolute -top-2 -right-2 bg-secondary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                0
              </span>
            </Link>
            <Link href="/account" className="text-gray-700 hover:text-primary">
              <FaUser size={24} />
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header; 