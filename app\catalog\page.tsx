'use client';

import React from 'react';
import Link from 'next/link';

import ProductCard from '@/components/ui/ProductCard';
import { ProductGridSkeleton } from '@/components/ui/Loading';
import { useProducts } from '@/hooks/useProducts';

// 分类列表
const categories = [
  { id: 'all', name: '全部' },
  { id: 'women', name: '女装' },
  { id: 'men', name: '男装' },
  { id: 'shoes', name: '鞋子' },
  { id: 'accessories', name: '配饰' },
  { id: 'sports', name: '运动' },
];



export default function CatalogPage() {
  const { products, loading, error } = useProducts();

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">全部商品</h1>
      
      {/* 筛选区域 */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 mb-4">
          {categories.map((cat) => (
            <Link
              key={cat.id}
              href={cat.id === 'all' ? '/catalog' : `/catalog/${cat.id}`}
              className={`px-4 py-2 border rounded-full transition-colors ${
                cat.id === 'all' 
                  ? 'bg-primary text-white border-primary' 
                  : 'border-gray-300 hover:bg-primary hover:text-white hover:border-primary'
              }`}
            >
              {cat.name}
            </Link>
          ))}
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            显示 <span className="font-medium">{products.length}</span> 件商品
          </div>
          
          <div className="flex items-center">
            <label htmlFor="sort" className="mr-2 text-gray-600">排序:</label>
            <select
              id="sort"
              className="border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-primary"
            >
              <option value="newest">最新上架</option>
              <option value="price-low">价格从低到高</option>
              <option value="price-high">价格从高到低</option>
              <option value="popular">热门商品</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 商品列表 */}
      {loading && <ProductGridSkeleton count={12} />}

      {error && (
        <div className="text-center py-12">
          <p className="text-xl text-red-500">加载失败: {error}</p>
        </div>
      )}

      {!loading && !error && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.length > 0 ? (
            products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-xl text-gray-500">暂无商品</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 