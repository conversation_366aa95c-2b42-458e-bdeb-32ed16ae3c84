import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { IMAGES } from '@/utils/imageConstants';
import { IMAGES, IMAGE_SIZES } from '@/utils/imageConstants';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { getProducts } from '../../utils/wordpress';

// 产品类型定义
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isNew: boolean;
  isSale: boolean;
}

// 分类列表
const categories = [
  { id: 'all', name: '全部' },
  { id: 'women', name: '女装' },
  { id: 'men', name: '男装' },
  { id: 'shoes', name: '鞋子' },
  { id: 'accessories', name: '配饰' },
  { id: 'sports', name: '运动' },
];

// 备用数据 - 当WordPress API不可用时使用
const allProducts: Product[] = [
  {
    id: '1',
    name: '夏季轻薄连衣裙',
    price: 299,
    originalPrice: 399,
    image: IMAGES.product(1),
    category: '女装',
    isNew: true,
    isSale: true,
  },
  {
    id: '2',
    name: '男士休闲西装外套',
    price: 599,
    originalPrice: 699,
    image: IMAGES.product(2),
    category: '男装',
    isNew: false,
    isSale: true,
  },
  {
    id: '3',
    name: '时尚牛仔裤',
    price: 199,
    image: IMAGES.product(3),
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '4',
    name: '运动休闲鞋',
    price: 399,
    image: IMAGES.product(4),
    category: '鞋子',
    isNew: false,
    isSale: false,
  },
  {
    id: '5',
    name: '真丝衬衫',
    price: 499,
    originalPrice: 599,
    image: IMAGES.product(5),
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '6',
    name: '休闲裤',
    price: 299,
    image: '/images/product2.jpg',
    category: '男装',
    isNew: false,
    isSale: true,
  },
  {
    id: '7',
    name: '针织开衫',
    price: 359,
    originalPrice: 459,
    image: '/images/product3.jpg',
    category: '女装',
    isNew: true,
    isSale: true,
  },
  {
    id: '8',
    name: '运动套装',
    price: 599,
    image: '/images/product4.jpg',
    category: '运动',
    isNew: false,
    isSale: false,
  },
];

// 将WordPress产品数据转换为前端格式
function formatProduct(wpProduct: any): Product {
  return {
    id: wpProduct.id.toString(),
    name: wpProduct.name,
    price: parseFloat(wpProduct.price || '0'),
    originalPrice: wpProduct.regular_price ? parseFloat(wpProduct.regular_price) : undefined,
    image: wpProduct.images && wpProduct.images.length > 0 ? wpProduct.images[0].src : '/images/product1.jpg',
    category: wpProduct.categories && wpProduct.categories.length > 0 ? wpProduct.categories[0].name : '其他',
    isNew: wpProduct.tags && wpProduct.tags.some((tag: any) => tag.name === '新品'),
    isSale: wpProduct.on_sale,
  };
}

export default async function CatalogPage() {
  // 尝试从WordPress获取产品数据
  let products: Product[] = [];
  
  try {
    // 使用备用数据
    products = allProducts;
    
    // 注意：WordPress API集成暂时注释掉，等配置好WordPress后再启用
    /*
    // 获取WordPress产品
    const wpProducts = await getProducts();
    products = wpProducts.map(formatProduct);
    */
  } catch (error) {
    console.error('从WordPress获取产品失败，使用备用数据:', error);
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">全部商品</h1>
      
      {/* 筛选区域 */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 mb-4">
          {categories.map((cat) => (
            <Link
              key={cat.id}
              href={cat.id === 'all' ? '/catalog' : `/catalog/${cat.id}`}
              className={`px-4 py-2 border rounded-full transition-colors ${
                cat.id === 'all' 
                  ? 'bg-primary text-white border-primary' 
                  : 'border-gray-300 hover:bg-primary hover:text-white hover:border-primary'
              }`}
            >
              {cat.name}
            </Link>
          ))}
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            显示 <span className="font-medium">{products.length}</span> 件商品
          </div>
          
          <div className="flex items-center">
            <label htmlFor="sort" className="mr-2 text-gray-600">排序:</label>
            <select
              id="sort"
              className="border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-primary"
            >
              <option value="newest">最新上架</option>
              <option value="price-low">价格从低到高</option>
              <option value="price-high">价格从高到低</option>
              <option value="popular">热门商品</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 商品列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.length > 0 ? (
          products.map((product: Product) => (
            <div key={product.id} className="product-card">
              <div className="product-image">
                <Link href={`/products/detail/${product.id}`}>
                  <OptimizedImage
                    src={product.image}
                    alt={product.name}
                    fill
                    className="product-image-img"
                    fallbackType="product"
                    fallbackId={product.id}
                    sizes={IMAGE_SIZES.product.card}
                    quality={75}
                  />
                </Link>

                {/* 产品徽章 */}
                <div className="product-badges">
                  {product.isNew && (
                    <span className="badge badge-primary">新品</span>
                  )}
                  {product.isSale && (
                    <span className="badge badge-secondary">特价</span>
                  )}
                </div>

                {/* 快捷操作按钮 */}
                <div className="product-actions">
                  <button className="product-action-btn" title="添加到收藏">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600 hover:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </button>
                  <button className="product-action-btn" title="快速查看">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600 hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="product-info">
                <div className="product-category">{product.category}</div>
                <Link href={`/products/detail/${product.id}`}>
                  <h3 className="product-name">{product.name}</h3>
                </Link>
                <div className="product-price-container">
                  <div className="flex items-center">
                    <span className="product-price">¥{product.price.toFixed(2)}</span>
                    {product.originalPrice && product.originalPrice > product.price && (
                      <span className="product-original-price">
                        ¥{product.originalPrice.toFixed(2)}
                      </span>
                    )}
                  </div>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="product-discount">
                      -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                    </span>
                  )}
                </div>
                <button className="product-add-to-cart">
                  加入购物车
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-xl text-gray-500">暂无商品</p>
          </div>
        )}
      </div>
    </div>
  );
} 