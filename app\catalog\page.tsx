import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getProducts } from '../../utils/wordpress';

// 产品类型定义
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isNew: boolean;
  isSale: boolean;
}

// 分类列表
const categories = [
  { id: 'all', name: '全部' },
  { id: 'women', name: '女装' },
  { id: 'men', name: '男装' },
  { id: 'shoes', name: '鞋子' },
  { id: 'accessories', name: '配饰' },
  { id: 'sports', name: '运动' },
];

// 备用数据 - 当WordPress API不可用时使用
const allProducts: Product[] = [
  {
    id: '1',
    name: '夏季轻薄连衣裙',
    price: 299,
    originalPrice: 399,
    image: '/images/product1.jpg',
    category: '女装',
    isNew: true,
    isSale: true,
  },
  {
    id: '2',
    name: '男士休闲西装外套',
    price: 599,
    originalPrice: 699,
    image: '/images/product2.jpg',
    category: '男装',
    isNew: false,
    isSale: true,
  },
  {
    id: '3',
    name: '时尚牛仔裤',
    price: 199,
    image: '/images/product3.jpg',
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '4',
    name: '运动休闲鞋',
    price: 399,
    image: '/images/product4.jpg',
    category: '鞋子',
    isNew: false,
    isSale: false,
  },
  {
    id: '5',
    name: '真丝衬衫',
    price: 499,
    originalPrice: 599,
    image: '/images/product1.jpg',
    category: '女装',
    isNew: true,
    isSale: false,
  },
  {
    id: '6',
    name: '休闲裤',
    price: 299,
    image: '/images/product2.jpg',
    category: '男装',
    isNew: false,
    isSale: true,
  },
  {
    id: '7',
    name: '针织开衫',
    price: 359,
    originalPrice: 459,
    image: '/images/product3.jpg',
    category: '女装',
    isNew: true,
    isSale: true,
  },
  {
    id: '8',
    name: '运动套装',
    price: 599,
    image: '/images/product4.jpg',
    category: '运动',
    isNew: false,
    isSale: false,
  },
];

// 将WordPress产品数据转换为前端格式
function formatProduct(wpProduct: any): Product {
  return {
    id: wpProduct.id.toString(),
    name: wpProduct.name,
    price: parseFloat(wpProduct.price || '0'),
    originalPrice: wpProduct.regular_price ? parseFloat(wpProduct.regular_price) : undefined,
    image: wpProduct.images && wpProduct.images.length > 0 ? wpProduct.images[0].src : '/images/product1.jpg',
    category: wpProduct.categories && wpProduct.categories.length > 0 ? wpProduct.categories[0].name : '其他',
    isNew: wpProduct.tags && wpProduct.tags.some((tag: any) => tag.name === '新品'),
    isSale: wpProduct.on_sale,
  };
}

export default async function CatalogPage() {
  // 尝试从WordPress获取产品数据
  let products: Product[] = [];
  
  try {
    // 使用备用数据
    products = allProducts;
    
    // 注意：WordPress API集成暂时注释掉，等配置好WordPress后再启用
    /*
    // 获取WordPress产品
    const wpProducts = await getProducts();
    products = wpProducts.map(formatProduct);
    */
  } catch (error) {
    console.error('从WordPress获取产品失败，使用备用数据:', error);
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">全部商品</h1>
      
      {/* 筛选区域 */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 mb-4">
          {categories.map((cat) => (
            <Link
              key={cat.id}
              href={cat.id === 'all' ? '/catalog' : `/catalog/${cat.id}`}
              className={`px-4 py-2 border rounded-full transition-colors ${
                cat.id === 'all' 
                  ? 'bg-primary text-white border-primary' 
                  : 'border-gray-300 hover:bg-primary hover:text-white hover:border-primary'
              }`}
            >
              {cat.name}
            </Link>
          ))}
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-gray-600">
            显示 <span className="font-medium">{products.length}</span> 件商品
          </div>
          
          <div className="flex items-center">
            <label htmlFor="sort" className="mr-2 text-gray-600">排序:</label>
            <select
              id="sort"
              className="border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-primary"
            >
              <option value="newest">最新上架</option>
              <option value="price-low">价格从低到高</option>
              <option value="price-high">价格从高到低</option>
              <option value="popular">热门商品</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* 商品列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.length > 0 ? (
          products.map((product: Product) => (
            <div key={product.id} className="card group relative">
              <div className="relative overflow-hidden aspect-[3/4]">
                <Link href={`/products/detail/${product.id}`}>
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </Link>
                <div className="absolute top-2 left-2 flex flex-col gap-2">
                  {product.isNew && (
                    <span className="bg-primary text-white px-2 py-1 text-xs font-medium rounded">
                      新品
                    </span>
                  )}
                  {product.isSale && (
                    <span className="bg-secondary text-white px-2 py-1 text-xs font-medium rounded">
                      特价
                    </span>
                  )}
                </div>
              </div>
              <div className="p-4">
                <div className="text-sm text-gray-500 mb-1">{product.category}</div>
                <Link href={`/products/detail/${product.id}`} className="block">
                  <h3 className="font-medium text-lg mb-2 hover:text-primary transition-colors">
                    {product.name}
                  </h3>
                </Link>
                <div className="flex items-center">
                  <span className="font-bold text-lg">¥{product.price.toFixed(2)}</span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-gray-400 line-through ml-2">
                      ¥{product.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-4 text-center py-12">
            <p className="text-xl text-gray-500">暂无商品</p>
          </div>
        )}
      </div>
    </div>
  );
} 