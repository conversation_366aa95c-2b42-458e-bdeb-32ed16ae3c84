import React from 'react';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-white pt-12 pb-6">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 关于我们 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">关于我们</h3>
            <p className="text-gray-400 mb-4">
              时尚服饰是一家专注于提供高品质时尚服装的电商平台，致力于为客户提供最佳的购物体验。
            </p>
          </div>

          {/* 客户服务 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">客户服务</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="text-gray-400 hover:text-white">
                  帮助中心
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-gray-400 hover:text-white">
                  配送信息
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-gray-400 hover:text-white">
                  退换货政策
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white">
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          {/* 快速链接 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products/new" className="text-gray-400 hover:text-white">
                  新品上市
                </Link>
              </li>
              <li>
                <Link href="/products/bestsellers" className="text-gray-400 hover:text-white">
                  热销商品
                </Link>
              </li>
              <li>
                <Link href="/products/sale" className="text-gray-400 hover:text-white">
                  特价商品
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-400 hover:text-white">
                  时尚博客
                </Link>
              </li>
            </ul>
          </div>

          {/* 订阅 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">订阅我们</h3>
            <p className="text-gray-400 mb-4">
              订阅我们的电子邮件，获取最新产品和促销信息。
            </p>
            <form className="flex">
              <input
                type="email"
                placeholder="您的邮箱地址"
                className="px-4 py-2 w-full rounded-l-md focus:outline-none text-gray-900"
              />
              <button
                type="submit"
                className="bg-primary hover:bg-primary-dark px-4 py-2 rounded-r-md"
              >
                订阅
              </button>
            </form>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} 时尚服饰. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 