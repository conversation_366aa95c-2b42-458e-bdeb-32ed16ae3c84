'use client';

import { useState, useEffect } from 'react';
import { FALLBACK_PRODUCTS } from '@/utils/mockData';

export interface Product {
  id: number;
  name: string;
  price: string;
  regular_price?: string;
  sale_price?: string;
  on_sale?: boolean;
  images?: Array<{ id: number; src: string; name: string; alt: string }>;
  stock_status?: string;
  categories?: Array<{ id: number; name: string; slug: string }>;
  description?: string;
  short_description?: string;
  attributes?: Array<{ id: number; name: string; options: string[] }>;
}

export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 使用静态数据
        setProducts(FALLBACK_PRODUCTS);
        setError(null);
      } catch (err) {
        setError('获取产品数据失败');
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  return { products, loading, error };
}

export function useProduct(id: number) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 从静态数据中查找产品
        const foundProduct = FALLBACK_PRODUCTS.find(p => p.id === id);
        if (foundProduct) {
          setProduct(foundProduct);
          setError(null);
        } else {
          setError('产品未找到');
        }
      } catch (err) {
        setError('获取产品详情失败');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  return { product, loading, error };
}

export function useProductsByCategory(category: string) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProductsByCategory = async () => {
      try {
        setLoading(true);
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 400));
        
        // 根据分类过滤产品
        const filteredProducts = FALLBACK_PRODUCTS.filter(product => 
          product.categories?.some(cat => cat.slug === category)
        );
        
        setProducts(filteredProducts);
        setError(null);
      } catch (err) {
        setError('获取分类产品失败');
        console.error('Error fetching products by category:', err);
      } finally {
        setLoading(false);
      }
    };

    if (category) {
      fetchProductsByCategory();
    }
  }, [category]);

  return { products, loading, error };
}

export function useFeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        setLoading(true);
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 获取特色产品（前8个产品）
        const featuredProducts = FALLBACK_PRODUCTS.slice(0, 8);
        
        setProducts(featuredProducts);
        setError(null);
      } catch (err) {
        setError('获取特色产品失败');
        console.error('Error fetching featured products:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  return { products, loading, error };
}
