import React from 'react';
import Link from 'next/link';
import { 
  FaHome, 
  FaBox, 
  FaUsers, 
  FaShoppingCart, 
  FaChartBar, 
  FaTags, 
  FaCog, 
  FaSignOutAlt 
} from 'react-icons/fa';

interface SidebarProps {
  active: string;
}

const Sidebar: React.FC<SidebarProps> = ({ active }) => {
  const menuItems = [
    { id: 'dashboard', label: '仪表盘', icon: <FaHome size={18} />, href: '/admin' },
    { id: 'products', label: '商品管理', icon: <FaBox size={18} />, href: '/admin/products' },
    { id: 'categories', label: '分类管理', icon: <FaTags size={18} />, href: '/admin/categories' },
    { id: 'orders', label: '订单管理', icon: <FaShoppingCart size={18} />, href: '/admin/orders' },
    { id: 'users', label: '用户管理', icon: <FaUsers size={18} />, href: '/admin/users' },
    { id: 'analytics', label: '数据分析', icon: <FaChartBar size={18} />, href: '/admin/analytics' },
    { id: 'settings', label: '系统设置', icon: <FaCog size={18} />, href: '/admin/settings' },
  ];

  return (
    <div className="bg-gray-800 text-white w-64 min-h-screen flex flex-col">
      <div className="p-4 border-b border-gray-700">
        <h2 className="text-xl font-bold">时尚服饰管理后台</h2>
      </div>
      
      <nav className="flex-grow py-4">
        <ul>
          {menuItems.map((item) => (
            <li key={item.id}>
              <Link 
                href={item.href}
                className={`flex items-center px-6 py-3 hover:bg-gray-700 transition-colors ${
                  active === item.id ? 'bg-gray-700 border-l-4 border-primary' : ''
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className="p-4 border-t border-gray-700">
        <button className="flex items-center text-gray-400 hover:text-white transition-colors">
          <FaSignOutAlt size={18} className="mr-3" />
          退出登录
        </button>
      </div>
    </div>
  );
};

export default Sidebar; 